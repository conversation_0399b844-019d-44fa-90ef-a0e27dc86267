const fetch = require('node-fetch');
const FormData = require('form-data');
require('dotenv').config();

async function testDocumentUpload() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('Testing document upload with current vector indexing...');
  
  // First, login to get a token
  console.log('\n🔐 Logging in...');
  
  const loginResponse = await fetch(`${baseUrl}/users/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Demo123!',
    }),
  });

  if (!loginResponse.ok) {
    console.error('❌ Login failed:', await loginResponse.text());
    return;
  }

  const loginResult = await loginResponse.json();
  const token = loginResult.result.accessToken;
  console.log('✅ Login successful');

  // Get or create a ChatAI app
  console.log('\n📱 Getting ChatAI apps...');
  
  const appsResponse = await fetch(`${baseUrl}/users/app/chatai/get-all-chatais`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!appsResponse.ok) {
    console.error('❌ Failed to get apps:', await appsResponse.text());
    return;
  }

  const appsResult = await appsResponse.json();
  let appId;

  if (appsResult.result && appsResult.result.length > 0) {
    appId = appsResult.result[0].app.id;
    console.log('✅ Using existing app:', appId);
  } else {
    // Create a new app first
    console.log('📱 Creating new application...');
    
    const createAppResponse = await fetch(`${baseUrl}/users/app/create-app`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test ChatAI App',
        description: 'Test app for vector indexing',
      }),
    });

    if (!createAppResponse.ok) {
      console.error('❌ Failed to create app:', await createAppResponse.text());
      return;
    }

    const createAppResult = await createAppResponse.json();
    appId = createAppResult.result.id;
    console.log('✅ Created new app:', appId);

    // Setup ChatAI for the app
    console.log('🤖 Setting up ChatAI...');
    
    const setupChatAiResponse = await fetch(`${baseUrl}/users/app/chatai/setup-chatai`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        appId: appId,
        name: 'Test ChatAI',
        description: 'Test ChatAI for vector indexing',
      }),
    });

    if (!setupChatAiResponse.ok) {
      console.error('❌ Failed to setup ChatAI:', await setupChatAiResponse.text());
      return;
    }

    console.log('✅ ChatAI setup successful');
  }

  // Now test document upload
  console.log('\n📄 Testing document upload...');
  
  const formData = new FormData();
  
  // Create a test PDF-like content
  const testContent = Buffer.from(`Invoice
# Invoice number  809BEF6C 0001

Date of issue: June 16, 2025
Date due: June 16, 2025

# Bill to
OpenRouter, Inc
169 Madison Avenue
#2404
New York, New York 10016
United States

Antier solutions pvt ltd
C 208, Phase 8B, Industrial Area, Sector 74, Sahibzada Ajit Singh Nagar,
Punjab 160059
mohali 160059
Punjab
India

$10.95 USD due June 16, 2025

# OpenRouter Purchase
| Description        | Qty | Unit price | Amount |
| ------------------ | --- | ---------- | ------ |
| OpenRouter Credits | 1   | $10.95     | $10.95 |

Subtotal: $10.95
Total: $10.95
Amount due: $10.95 USD
OpenRouter EIN: 92 3594255`);

  formData.append('file', testContent, {
    filename: 'test-invoice.pdf',
    contentType: 'application/pdf',
  });
  
  formData.append('appId', appId);
  formData.append('title', 'Test Invoice Document');
  formData.append('description', 'Test document for vector indexing');

  const uploadResponse = await fetch(`${baseUrl}/users/app/chatai/upload-document`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      ...formData.getHeaders(),
    },
    body: formData,
  });

  console.log('Upload response status:', uploadResponse.status);

  if (!uploadResponse.ok) {
    const errorText = await uploadResponse.text();
    console.error('❌ Document upload failed:', errorText);
    return;
  }

  const uploadResult = await uploadResponse.json();
  console.log('✅ Document upload initiated!');
  console.log('Document ID:', uploadResult.result.documentId);
  console.log('Status:', uploadResult.result.status);

  // Wait and check document status
  console.log('\n⏳ Waiting for document processing...');
  
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
    attempts++;
    
    console.log(`📊 Checking status (attempt ${attempts}/${maxAttempts})...`);
    
    const documentsResponse = await fetch(`${baseUrl}/users/app/chatai/get-documents?appId=${appId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (documentsResponse.ok) {
      const documentsResult = await documentsResponse.json();
      const document = documentsResult.result.find(doc => doc.id === uploadResult.result.documentId);
      
      if (document) {
        console.log(`📄 Document status: ${document.status}`);
        
        if (document.errorMessage) {
          console.log(`⚠️  Error message: ${document.errorMessage}`);
        }
        
        if (document.indexId) {
          console.log(`🔍 Index ID: ${document.indexId}`);
        }
        
        if (document.status === 'ready' || document.status === 'error') {
          console.log('\n✅ Document processing completed!');
          console.log('Final document details:');
          console.log('- Status:', document.status);
          console.log('- Index ID:', document.indexId);
          console.log('- Error Message:', document.errorMessage || 'None');
          console.log('- Page Count:', document.pageCount);
          console.log('- Word Count:', document.wordCount);
          
          if (document.summary) {
            console.log('- Summary available:', !!document.summary);
          }
          
          break;
        }
      }
    }
  }
  
  if (attempts >= maxAttempts) {
    console.log('⏰ Timeout waiting for document processing');
  }
}

testDocumentUpload().catch(console.error);
