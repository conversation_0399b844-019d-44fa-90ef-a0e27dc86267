const fetch = require('node-fetch');
const FormData = require('form-data');
require('dotenv').config();

async function testNewLlamaCloudAPI() {
  const apiKey = process.env.LLAMA_CLOUD_API_KEY;
  const baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';

  console.log('Testing NEW LlamaCloud Pipeline API...');
  console.log('API Key configured:', !!apiKey);
  console.log('API Key (first 10 chars):', apiKey ? apiKey.substring(0, 10) + '...' : 'NOT SET');

  if (!apiKey) {
    console.error('❌ LLAMA_CLOUD_API_KEY not found in environment variables');
    return;
  }

  // Test 1: Create a pipeline with document
  console.log('\n🧪 Test 1: Creating a test pipeline...');

  const pipelinePayload = {
    name: `test_pipeline_${Date.now()}`,
    embedding_config: {
      type: 'HUGGINGFACE_API_EMBEDDING',
      component: {
        model_name: 'sentence-transformers/all-MiniLM-L6-v2',
        api_key: 'dummy', // Some HuggingFace models might not require API key
      },
    },
    transform_config: {
      mode: 'auto',
      config: {
        chunk_size: 1024,
        chunk_overlap: 20,
      },
    },
    data_sink_id: null, // Use managed sink
  };

  try {
    const pipelineResponse = await fetch(`${baseUrl}/pipelines`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pipelinePayload),
    });

    console.log('Pipeline creation status:', pipelineResponse.status);
    console.log('Pipeline headers:', Object.fromEntries(pipelineResponse.headers.entries()));

    if (!pipelineResponse.ok) {
      const errorText = await pipelineResponse.text();
      console.error('❌ Pipeline Creation Error:', errorText);

      // Try to parse as JSON for better error details
      try {
        const errorJson = JSON.parse(errorText);
        console.error('❌ Parsed Pipeline Error:', JSON.stringify(errorJson, null, 2));
      } catch (e) {
        console.error('❌ Raw Pipeline Error Text:', errorText);
      }
      return;
    }

    const pipelineResult = await pipelineResponse.json();
    console.log('✅ Pipeline created successfully!');
    console.log('Pipeline ID:', pipelineResult.id);
    console.log('Full pipeline response:', JSON.stringify(pipelineResult, null, 2));

    // Test 2: Add a document to the pipeline
    console.log('\n🧪 Test 2: Adding document to pipeline...');

    const documentsPayload = [
      {
        text: "This is a test document for vector indexing. It contains sample text to verify that the LlamaCloud API is working correctly with the new pipeline structure.",
        metadata: {
          filename: "test.txt",
          page_count: 1,
          word_count: 25
        }
      }
    ];

    const documentsResponse = await fetch(`${baseUrl}/pipelines/${pipelineResult.id}/documents/batch`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(documentsPayload),
    });

    if (!documentsResponse.ok) {
      const documentsError = await documentsResponse.text();
      console.error('❌ Documents Error:', documentsError);
    } else {
      const documentsResult = await documentsResponse.json();
      console.log('✅ Documents added successfully!');
      console.log('Documents response:', JSON.stringify(documentsResult, null, 2));

      // Wait a bit for indexing to complete
      console.log('\n⏳ Waiting 5 seconds for indexing to complete...');
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Test 3: Try to search the pipeline
      console.log('\n🧪 Test 3: Testing search from pipeline...');

      const searchPayload = {
        query: "test document",
        search_filters: {},
        similarity_top_k: 3
      };

      const searchResponse = await fetch(`${baseUrl}/pipelines/${pipelineResult.id}/search`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchPayload),
      });

      if (!searchResponse.ok) {
        const searchError = await searchResponse.text();
        console.error('❌ Search Error:', searchError);
      } else {
        const searchResult = await searchResponse.json();
        console.log('✅ Search successful!');
        console.log('Retrieved nodes:', searchResult.retrieval_nodes?.length || 0);
        console.log('Search response:', JSON.stringify(searchResult, null, 2));
      }
    }

    // Test 4: Clean up - delete the test pipeline
    console.log('\n🧪 Test 4: Cleaning up test pipeline...');

    const deleteResponse = await fetch(`${baseUrl}/pipelines/${pipelineResult.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
    });

    if (!deleteResponse.ok) {
      const deleteError = await deleteResponse.text();
      console.error('❌ Delete Error:', deleteError);
    } else {
      console.log('✅ Test pipeline deleted successfully!');
    }

  } catch (error) {
    console.error('❌ Network/Connection Error:', error.message);
    console.error('Error details:', error);
  }
}

// Test with actual invoice document using new API
async function testWithActualInvoice() {
  const apiKey = process.env.LLAMA_CLOUD_API_KEY;
  const baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';

  console.log('\n🧪 Testing with actual invoice document using new API...');

  try {
    // Create pipeline for invoice
    const pipelinePayload = {
      name: `invoice_pipeline_${Date.now()}`,
      embedding_config: {
        type: 'HUGGINGFACE_API_EMBEDDING',
        component: {
          model_name: 'sentence-transformers/all-MiniLM-L6-v2',
          api_key: 'dummy',
        },
      },
      transform_config: {
        mode: 'auto',
        config: {
          chunk_size: 1024,
          chunk_overlap: 20,
        },
      },
      data_sink_id: null,
    };

    const pipelineResponse = await fetch(`${baseUrl}/pipelines`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pipelinePayload),
    });

    if (!pipelineResponse.ok) {
      const errorText = await pipelineResponse.text();
      console.error('❌ Invoice pipeline creation failed:', errorText);
      return;
    }

    const pipelineResult = await pipelineResponse.json();
    console.log('✅ Invoice pipeline created!');
    console.log('Pipeline ID:', pipelineResult.id);

    // Add invoice document
    const invoiceDocument = [{
      text: `Invoice
# Invoice number  809BEF6C 0001

Date of issue: June 16, 2025
Date due: June 16, 2025

# Bill to
OpenRouter, Inc
169 Madison Avenue
#2404
New York, New York 10016
United States

Antier solutions pvt ltd
C 208, Phase 8B, Industrial Area, Sector 74, Sahibzada Ajit Singh Nagar,
Punjab 160059
mohali 160059
Punjab
India

$10.95 USD due June 16, 2025

# OpenRouter Purchase
| Description        | Qty | Unit price | Amount |
| ------------------ | --- | ---------- | ------ |
| OpenRouter Credits | 1   | $10.95     | $10.95 |

Subtotal: $10.95
Total: $10.95
Amount due: $10.95 USD
OpenRouter EIN: 92 3594255`,
      metadata: {
        filename: "Invoice-809BEF6C-0001.pdf",
        page_count: 1,
        word_count: 117
      }
    }];

    const documentsResponse = await fetch(`${baseUrl}/pipelines/${pipelineResult.id}/documents/batch`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invoiceDocument),
    });

    if (!documentsResponse.ok) {
      const errorText = await documentsResponse.text();
      console.error('❌ Invoice document addition failed:', errorText);
    } else {
      console.log('✅ Invoice document added successfully!');

      // Test search
      console.log('\n⏳ Waiting for indexing...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      const searchResponse = await fetch(`${baseUrl}/pipelines/${pipelineResult.id}/search`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: "OpenRouter invoice amount",
          similarity_top_k: 3
        }),
      });

      if (searchResponse.ok) {
        const searchResult = await searchResponse.json();
        console.log('✅ Invoice search successful!');
        console.log('Found nodes:', searchResult.retrieval_nodes?.length || 0);
      }
    }

    // Clean up
    await fetch(`${baseUrl}/pipelines/${pipelineResult.id}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${apiKey}` },
    });
    console.log('✅ Invoice pipeline cleaned up');

  } catch (error) {
    console.error('❌ Invoice test error:', error.message);
  }
}

// Run the tests
async function runAllTests() {
  await testNewLlamaCloudAPI();
  await testWithActualInvoice();
}

runAllTests().catch(console.error);
