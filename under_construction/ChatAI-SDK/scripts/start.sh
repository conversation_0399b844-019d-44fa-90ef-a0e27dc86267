#!/bin/bash

# ChatAI SDK Service Startup Script
# This script helps you start the ChatAI SDK service with proper configuration

set -e

echo "🚀 ChatAI SDK Service Startup"
echo "=============================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="16.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please install Node.js 16+ and try again."
    exit 1
fi

echo "✅ Node.js version: $NODE_VERSION"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Please run this script from the ChatAI-SDK directory."
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
else
    echo "✅ Dependencies already installed"
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your API keys before starting the service."
    echo "   Required: LLAMA_CLOUD_API_KEY, OPENROUTER_API_KEY"
    exit 1
fi

# Validate required environment variables
source .env

MISSING_VARS=()

if [ -z "$LLAMA_CLOUD_API_KEY" ] || [ "$LLAMA_CLOUD_API_KEY" = "llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp" ]; then
    MISSING_VARS+=("LLAMA_CLOUD_API_KEY")
fi

if [ -z "$OPENROUTER_API_KEY" ] || [ "$OPENROUTER_API_KEY" = "sk-or-your-openrouter-api-key-here" ]; then
    MISSING_VARS+=("OPENROUTER_API_KEY")
fi

if [ ${#MISSING_VARS[@]} -ne 0 ]; then
    echo "❌ Missing or default values for required environment variables:"
    for var in "${MISSING_VARS[@]}"; do
        echo "   - $var"
    done
    echo "📝 Please update your .env file with actual API keys."
    exit 1
fi

echo "✅ Environment variables configured"

# Check if User-Service is running (optional)
USER_SERVICE_URL=${USER_SERVICE_URL:-"http://localhost:3000"}
echo "🔍 Checking User-Service availability at $USER_SERVICE_URL..."

if curl -s --connect-timeout 5 "$USER_SERVICE_URL/health" > /dev/null 2>&1; then
    echo "✅ User-Service is running"
else
    echo "⚠️  User-Service not reachable at $USER_SERVICE_URL"
    echo "   The ChatAI SDK will still start, but document fetching may fail."
    echo "   Make sure User-Service is running before testing chat functionality."
fi

# Start the service
PORT=${PORT:-3001}
NODE_ENV=${NODE_ENV:-development}

echo ""
echo "🚀 Starting ChatAI SDK Service..."
echo "   Environment: $NODE_ENV"
echo "   Port: $PORT"
echo "   User-Service: $USER_SERVICE_URL"
echo ""

if [ "$NODE_ENV" = "development" ]; then
    echo "🔧 Starting in development mode with auto-reload..."
    npm run dev
else
    echo "🏭 Starting in production mode..."
    npm start
fi
