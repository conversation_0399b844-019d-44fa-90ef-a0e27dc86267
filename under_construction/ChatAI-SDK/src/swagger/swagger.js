const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'ChatAI SDK Service API',
      version: '1.0.0',
      description: 'Express SDK service for ChatAI with LlamaIndex and OpenRouter integration',
      contact: {
        name: 'ChatAI Team',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: 'http://localhost:3001',
        description: 'Development server'
      },
      {
        url: 'https://chatai-sdk.com',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        ChatRequest: {
          type: 'object',
          required: ['query'],
          properties: {
            query: {
              type: 'string',
              description: 'User query for chat',
              example: 'What is the main topic of the documents?',
              maxLength: 2000
            },
            sessionId: {
              type: 'string',
              format: 'uuid',
              description: 'Optional session ID for maintaining conversation context'
            },
            stream: {
              type: 'boolean',
              description: 'Enable streaming response',
              default: true
            },
            includeHistory: {
              type: 'boolean',
              description: 'Include chat history in context',
              default: false
            }
          }
        },
        ChatResponse: {
          type: 'object',
          properties: {
            error: {
              type: 'boolean',
              example: false
            },
            sessionId: {
              type: 'string',
              format: 'uuid',
              description: 'Session ID for future requests'
            },
            response: {
              type: 'string',
              description: 'Generated chat response'
            },
            documentsUsed: {
              type: 'integer',
              description: 'Number of documents used for context'
            },
            contextLength: {
              type: 'integer',
              description: 'Length of retrieved context'
            }
          }
        },
        StreamingEvent: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              enum: ['session', 'content', 'done', 'error'],
              description: 'Type of streaming event'
            },
            content: {
              type: 'string',
              description: 'Content chunk (for content type events)'
            },
            sessionId: {
              type: 'string',
              format: 'uuid',
              description: 'Session ID (for session type events)'
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: 'Event timestamp'
            },
            message: {
              type: 'string',
              description: 'Error message (for error type events)'
            }
          }
        },
        StreamingResponse: {
          type: 'string',
          description: 'Server-Sent Events stream with chat response',
          example: 'data: {"type":"session","sessionId":"uuid","timestamp":"2024-01-01T00:00:00.000Z"}\ndata: {"type":"content","content":"The main topic"}\ndata: {"type":"content","content":" of the documents is..."}\ndata: {"type":"done","timestamp":"2024-01-01T00:00:01.000Z"}'
        },
        SessionInfo: {
          type: 'object',
          properties: {
            error: {
              type: 'boolean',
              example: false
            },
            session: {
              type: 'object',
              properties: {
                sessionId: {
                  type: 'string',
                  format: 'uuid'
                },
                appId: {
                  type: 'string',
                  format: 'uuid'
                },
                documentsCount: {
                  type: 'integer'
                },
                timestamp: {
                  type: 'integer',
                  description: 'Session creation timestamp'
                },
                lastAccessed: {
                  type: 'integer',
                  description: 'Last access timestamp'
                },
                accessCount: {
                  type: 'integer',
                  description: 'Number of times session was accessed'
                }
              }
            }
          }
        },
        ServiceStats: {
          type: 'object',
          properties: {
            error: {
              type: 'boolean',
              example: false
            },
            stats: {
              type: 'object',
              properties: {
                totalSessions: {
                  type: 'integer',
                  description: 'Total active sessions'
                },
                totalApps: {
                  type: 'integer',
                  description: 'Total applications with sessions'
                },
                maxSessions: {
                  type: 'integer',
                  description: 'Maximum allowed sessions'
                },
                ttlMinutes: {
                  type: 'integer',
                  description: 'Session TTL in minutes'
                },
                cleanupIntervalMinutes: {
                  type: 'integer',
                  description: 'Cache cleanup interval in minutes'
                }
              }
            }
          }
        },
        HealthCheck: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              enum: ['healthy', 'unhealthy'],
              example: 'healthy'
            },
            timestamp: {
              type: 'string',
              format: 'date-time'
            },
            services: {
              type: 'object',
              properties: {
                llamaIndex: {
                  type: 'boolean',
                  description: 'LlamaIndex service status'
                },
                openRouter: {
                  type: 'boolean',
                  description: 'OpenRouter service status'
                },
                cache: {
                  type: 'boolean',
                  description: 'Cache service status'
                }
              }
            },
            cache: {
              $ref: '#/components/schemas/ServiceStats/properties/stats'
            }
          }
        },
        ErrorResponse: {
          type: 'object',
          properties: {
            error: {
              type: 'boolean',
              example: true
            },
            message: {
              type: 'string',
              description: 'Error message'
            },
            timestamp: {
              type: 'string',
              format: 'date-time'
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ['./src/routes/*.js', './src/controllers/*.js'], // paths to files containing OpenAPI definitions
};

const specs = swaggerJsdoc(options);

module.exports = {
  specs,
  swaggerUi,
  serve: swaggerUi.serve,
  setup: swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'ChatAI SDK API Documentation'
  })
};
