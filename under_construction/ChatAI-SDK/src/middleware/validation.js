const { validate: validateUUID } = require('uuid');
const userService = require('../services/userService');

/**
 * Validate chat request
 */
const validateChatRequest = async (req, res, next) => {
  const { appId } = req.params;
  const { query, sessionId, stream, includeHistory, apiKey, apikey } = req.query;
  // Handle both apiKey and apikey parameter names
  const actualApiKey = apiKey || apikey;
  const authToken = req.headers.authorization;
  const origin = req.headers.origin || req.headers.referer || 'unknown';

  // Validate appId (should be UUID)
  if (!appId || !validateUUID(appId)) {
    return res.status(400).json({
      error: true,
      message: 'Valid appId (UUID) is required'
    });
  }

  // Validate query
  if (!query || typeof query !== 'string' || query.trim().length === 0) {
    return res.status(400).json({
      error: true,
      message: 'Query is required and must be a non-empty string'
    });
  }

  // Validate query length
  if (query.length > 2000) {
    return res.status(400).json({
      error: true,
      message: 'Query is too long (max 2000 characters)'
    });
  }

  // Validate authorization header
  if (!authToken || !authToken.startsWith('Bearer ')) {
    return res.status(401).json({
      error: true,
      message: 'Authorization header with Bearer token is required'
    });
  }

  // API Key validation for ChatAI (comprehensive validation by User-Service)
  if (actualApiKey) {
    try {
      console.log(`🔑 Validating API key for request to appId: ${appId}`);
      const validationResult = await userService.validateApiKey(actualApiKey, origin, appId);

      // Store validation result in request for use in controllers
      // key-validator already performed: API key validation, app validation,
      // credit checking, origin validation, and service permissions
      req.chatAiValidation = validationResult;

      console.log(`✅ Comprehensive validation successful. Credits: ${validationResult.creditInfo?.creditsRemaining}, Status: ${validationResult.creditInfo?.subscriptionStatus}`);
    } catch (error) {
      console.error(`❌ API key validation failed: ${error.message}`);
      return res.status(403).json({
        error: true,
        message: error.message
      });
    }
  } else {
    console.log(`⚠️  No API key provided for appId: ${appId} - will perform basic validation in controller`);
  }

  // Validate sessionId if provided
  if (sessionId && !validateUUID(sessionId)) {
    return res.status(400).json({
      error: true,
      message: 'SessionId must be a valid UUID if provided'
    });
  }

  // Validate stream parameter (query params are strings, so check for 'true'/'false')
  if (stream !== undefined && !['true', 'false'].includes(stream)) {
    return res.status(400).json({
      error: true,
      message: 'Stream parameter must be "true" or "false"'
    });
  }

  // Validate includeHistory parameter (query params are strings, so check for 'true'/'false')
  if (includeHistory !== undefined && !['true', 'false'].includes(includeHistory)) {
    return res.status(400).json({
      error: true,
      message: 'IncludeHistory parameter must be "true" or "false"'
    });
  }

  // Sanitize query and store in req.query for consistency
  req.query.query = query.trim();

  next();
};

/**
 * Validate session ID parameter
 */
const validateSessionId = (req, res, next) => {
  const { sessionId } = req.params;

  if (!sessionId || !validateUUID(sessionId)) {
    return res.status(400).json({
      error: true,
      message: 'Valid sessionId (UUID) is required'
    });
  }

  next();
};

/**
 * Validate app ID parameter
 */
const validateAppId = (req, res, next) => {
  const { appId } = req.params;

  if (!appId || !validateUUID(appId)) {
    return res.status(400).json({
      error: true,
      message: 'Valid appId (UUID) is required'
    });
  }

  next();
};

/**
 * Validate embed request (for iframe URL generation)
 */
const validateEmbedRequest = async (req, res, next) => {
  const { appId } = req.params;
  const { theme, width, height, apiKey, apikey } = req.query;
  // Handle both apiKey and apikey parameter names
  const actualApiKey = apiKey || apikey;
  const authToken = req.headers.authorization;
  const origin = req.headers.origin || req.headers.referer || 'unknown';

  // Validate appId (should be UUID)
  if (!appId || !validateUUID(appId)) {
    return res.status(400).json({
      error: true,
      message: 'Valid appId (UUID) is required'
    });
  }

  // Authorization is optional for embed requests (widget doesn't require auth)

  // API Key validation for widget requests (optional but comprehensive when provided)
  if (actualApiKey) {
    try {
      console.log(`🔑 Validating API key for widget request to appId: ${appId}`);
      const validationResult = await userService.validateApiKey(actualApiKey, origin, appId);

      // Store validation result in request for use in controllers
      // key-validator already performed comprehensive validation
      req.chatAiValidation = validationResult;

      console.log(`✅ Widget comprehensive validation successful. Credits: ${validationResult.creditInfo?.creditsRemaining}, Status: ${validationResult.creditInfo?.subscriptionStatus}`);
    } catch (error) {
      console.error(`❌ Widget API key validation failed: ${error.message}`);
      return res.status(403).json({
        error: true,
        message: error.message
      });
    }
  } else {
    console.log(`⚠️  No API key provided for widget appId: ${appId} - will perform basic validation in controller`);
  }

  // Validate theme if provided
  if (theme && !['light', 'dark'].includes(theme)) {
    return res.status(400).json({
      error: true,
      message: 'Theme must be either "light" or "dark"'
    });
  }

  // Validate dimensions if provided (basic validation)
  if (width && typeof width !== 'string') {
    return res.status(400).json({
      error: true,
      message: 'Width must be a string (e.g., "400px", "100%")'
    });
  }

  if (height && typeof height !== 'string') {
    return res.status(400).json({
      error: true,
      message: 'Height must be a string (e.g., "600px", "100vh")'
    });
  }

  next();
};

/**
 * General request validation middleware
 */
const validateRequest = (req, res, next) => {
  // Check content type for POST/PUT requests
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    if (!req.is('application/json')) {
      return res.status(400).json({
        error: true,
        message: 'Content-Type must be application/json'
      });
    }
  }

  // Check for common security headers
  const userAgent = req.get('User-Agent');
  if (!userAgent) {
    return res.status(400).json({
      error: true,
      message: 'User-Agent header is required'
    });
  }

  next();
};

/**
 * Error handling middleware for validation errors
 */
const handleValidationError = (error, req, res, next) => {
  if (error.type === 'entity.parse.failed') {
    return res.status(400).json({
      error: true,
      message: 'Invalid JSON in request body'
    });
  }

  if (error.type === 'entity.too.large') {
    return res.status(413).json({
      error: true,
      message: 'Request body too large'
    });
  }

  next(error);
};

module.exports = {
  validateChatRequest,
  validateSessionId,
  validateAppId,
  validateEmbedRequest,
  validateRequest,
  handleValidationError
};
