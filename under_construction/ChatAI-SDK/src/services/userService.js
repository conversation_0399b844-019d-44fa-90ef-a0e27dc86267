const fetch = require('node-fetch');
const config = require('../config');

class UserService {
  constructor() {
    this.baseUrl = config.userService.url;
    this.chatAiOrigin = config.chatAiOrigin;
  }

  /**
   * Fetch documents for a given appId
   * @param {string} appId - Application ID
   * @param {string} authToken - JWT token for authentication
   * @returns {Promise<Array>} Array of documents with retriever IDs
   */
  async getDocuments(appId, authToken) {
    try {
      console.log(`\n🔗 ═══════════════════════════════════════════════════════════════`);
      console.log(`👤 USER SERVICE CALL - GET DOCUMENTS`);
      console.log(`📋 AppId: ${appId}`);
      console.log(`🔐 Auth Token: ${authToken ? '✅ Provided' : '❌ Not provided'}`);
      console.log(`🕐 Timestamp: ${new Date().toISOString()}`);
      console.log(`═══════════════════════════════════════════════════════════════\n`);

      const headers = {
        'Content-Type': 'application/json',
      };

      // Add authorization header only if authToken is provided
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      // Use widget endpoint if no auth token provided
      const endpoint = authToken
        ? `/users/app/chatai/get-documents?appId=${appId}`
        : `/users/app/chatai/widget/get-documents?appId=${appId}`;

      console.log(`🌐 Making request to: ${this.baseUrl}${endpoint}`);
      console.log(`📤 Request method: GET`);

      const response = await fetch(
        `${this.baseUrl}${endpoint}`,
        {
          method: 'GET',
          headers: headers,
        }
      );

      console.log(`📥 Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`❌ USER SERVICE ERROR: ${response.status} - ${errorText}`);
        throw new Error(`User Service error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.message || 'Failed to fetch documents');
      }

      // Filter ready documents with valid retriever IDs
      const readyDocuments = (result.result || []).filter(doc =>
        doc.status === 'ready' &&
        doc.indexId &&
        !doc.indexId.startsWith('fallback-')
      );

      console.log(`✅ USER SERVICE SUCCESS: Found ${readyDocuments.length} ready documents with valid retriever IDs`);
      console.log(`📊 Total documents in response: ${(result.result || []).length}`);

      // Log document details for debugging
      readyDocuments.forEach(doc => {
        console.log(`📄 Document: ${doc.filename}, indexId: ${doc.indexId}, hasText: ${!!doc.parsedData?.text}`);
      });

      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);
      return readyDocuments;

    } catch (error) {
      console.error(`❌ USER SERVICE ERROR - GET DOCUMENTS: ${error.message}`);
      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);
      throw error;
    }
  }

  /**
   * Validate API key and check credits using User-Service key-validator
   * @param {string} apiKey - Application API key
   * @param {string} origin - Request origin
   * @param {string} appId - Application ID
   * @returns {Promise<Object>} Validation result with credit info
   */
  async validateApiKey(apiKey, origin, appId) {
    try {
      console.log(`\n🔗 ═══════════════════════════════════════════════════════════════`);
      console.log(`🔑 USER SERVICE CALL - API KEY VALIDATION`);
      console.log(`🗝️  API Key: ${apiKey ? `${apiKey.substring(0, 20)}...` : 'Not provided'}`);
      console.log(`🌐 Origin: ${origin}`);
      console.log(`📋 AppId: ${appId}`);
      console.log(`🕐 Timestamp: ${new Date().toISOString()}`);
      console.log(`═══════════════════════════════════════════════════════════════\n`);

      const requestBody = {
        chainId: 1, // Default chainId for ChatAI
        apikey: apiKey,
        origin: origin,
        payload: {
          method: 'chat',
          params: [appId]
        },
        type: 'chatai'
      };

      console.log(`🌐 Making request to: ${this.baseUrl}/users/app/key-validator`);
      console.log(`📤 Request method: POST`);
      console.log(`📦 Request payload: ${JSON.stringify(requestBody, null, 2)}`);

      const response = await fetch(
        `${this.baseUrl}/users/app/key-validator`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Origin': this.chatAiOrigin
          },
          body: JSON.stringify(requestBody)
        }
      );

      console.log(`📥 Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`❌ USER SERVICE ERROR: ${response.status} - ${errorText}`);
        throw new Error(`Key validation failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      if (result.error) {
        console.log(`❌ USER SERVICE ERROR: ${result.message || 'API key validation failed'}`);
        throw new Error(result.message || 'API key validation failed');
      }

      const credits = result.result.creditInfo?.creditsRemaining || 'Unknown';
      const subscriptionStatus = result.result.creditInfo?.subscriptionStatus || 'Unknown';

      console.log(`✅ USER SERVICE SUCCESS: API key validated successfully`);
      console.log(`💳 Credits remaining: ${credits}`);
      console.log(`📊 Subscription status: ${subscriptionStatus}`);
      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);

      return result.result;

    } catch (error) {
      console.error(`❌ USER SERVICE ERROR - API KEY VALIDATION: ${error.message}`);
      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);
      throw error;
    }
  }

  /**
   * Validate if appId exists and user has access
   * @param {string} appId - Application ID
   * @param {string} authToken - JWT token for authentication
   * @returns {Promise<boolean>} True if valid
   */
  async validateAppId(appId, authToken) {
    try {
      console.log(`\n🔗 ═══════════════════════════════════════════════════════════════`);
      console.log(`🔍 USER SERVICE CALL - APP ID VALIDATION`);
      console.log(`📋 AppId: ${appId}`);
      console.log(`🔐 Auth Token: ${authToken ? '✅ Provided' : '❌ Not provided'}`);
      console.log(`🕐 Timestamp: ${new Date().toISOString()}`);
      console.log(`═══════════════════════════════════════════════════════════════\n`);

      const headers = {
        'Content-Type': 'application/json',
      };

      // Add authorization header only if authToken is provided
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      // Use widget endpoint if no auth token provided
      const endpoint = authToken
        ? `/users/app/chatai/get-single-chatai?appId=${appId}`
        : `/users/app/chatai/widget/get-single-chatai?appId=${appId}`;

      console.log(`🌐 Making request to: ${this.baseUrl}${endpoint}`);
      console.log(`📤 Request method: GET`);

      const response = await fetch(
        `${this.baseUrl}${endpoint}`,
        {
          method: 'GET',
          headers: headers,
        }
      );

      console.log(`📥 Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.log(`❌ USER SERVICE ERROR: App validation failed with status ${response.status}`);
        console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);
        return false;
      }

      const result = await response.json();
      const isValid = !result.error;

      if (isValid) {
        console.log(`✅ USER SERVICE SUCCESS: App ID validated successfully`);
      } else {
        console.log(`❌ USER SERVICE ERROR: App validation failed - ${result.message || 'Unknown error'}`);
      }

      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);
      return isValid;

    } catch (error) {
      console.error(`❌ USER SERVICE ERROR - APP ID VALIDATION: ${error.message}`);
      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);
      return false;
    }
  }
}

module.exports = new UserService();
