const userService = require('../services/userService');
const llamaIndexService = require('../services/llamaIndexService');
const openRouterService = require('../services/openRouterService');
const cacheService = require('../services/cacheService');

class ChatController {
  /**
   * Main chat endpoint with complete flow
   * GET /chat/:appId
   */
  async chat(req, res) {
    const { appId } = req.params;
    const { query, sessionId, stream = 'true', includeHistory = 'false', apiKey } = req.query;
    const authToken = req.headers.authorization?.replace('Bearer ', '');

    // Convert string query parameters to appropriate types
    const streamBool = stream === 'true' || stream === true;
    const includeHistoryBool = includeHistory === 'true' || includeHistory === true;

    try {
      // Validation
      if (!appId || !query) {
        return res.status(400).json({
          error: true,
          message: 'appId and query are required'
        });
      }

      // TODO: Enable apiKey validation when Kong is ready
      // if (!apiKey) {
      //   return res.status(400).json({
      //     error: true,
      //     message: 'Application API key is required in query parameters'
      //   });
      // }

      if (!authToken) {
        return res.status(401).json({
          error: true,
          message: 'Authorization token is required'
        });
      }

      console.log(`💬 Chat request for appId: ${appId}, query: "${query.substring(0, 50)}..."`);

      // Step 1: Check API key validation results (if provided)
      if (req.chatAiValidation) {
        const { creditInfo } = req.chatAiValidation;
        console.log(`💳 Credit info - Remaining: ${creditInfo.creditsRemaining}, Status: ${creditInfo.subscriptionStatus}, Can use: ${creditInfo.canUseService}`);

        // key-validator already checked credits, app validity, and permissions
        // No need for additional validation - key-validator is comprehensive
        console.log(`✅ API key validation successful - App and credits validated by User-Service`);
      } else {
        // Step 2: Fallback validation when no API key provided
        console.log(`🔍 No API key provided - performing basic app validation`);
        const isValidApp = await userService.validateAppId(appId, authToken);

        if (!isValidApp) {
          return res.status(404).json({
            error: true,
            message: 'Application not found or no ChatAI service configured for this application'
          });
        }

        console.log(`✅ Application validated: ${appId}`);
      }

      // Step 3: Get or create session
      const currentSessionId = cacheService.getOrCreateSession(appId, sessionId);

      // Step 4: Get documents (cached or fetch from User-Service)
      const documents = await this.getDocumentsForChat(currentSessionId, appId, authToken);

      if (!documents || documents.length === 0) {
        return res.status(404).json({
          error: true,
          message: 'No ready documents found for this application'
        });
      }

      // Step 5: Retrieve context from LlamaIndex
      const context = await llamaIndexService.retrieveFromMultipleDocuments(documents, query);

      // Step 6: Generate response
      if (streamBool) {
        await this.streamChatResponse(res, query, context, currentSessionId);
      } else {
        const response = await openRouterService.generateResponse(query, context);

        res.json({
          error: false,
          sessionId: currentSessionId,
          response,
          documentsUsed: documents.length,
          contextLength: context.length
        });
      }

    } catch (error) {
      console.error('❌ Chat error:', error.message);

      if (!res.headersSent) {
        res.status(500).json({
          error: true,
          message: 'Internal server error during chat processing'
        });
      }
    }
  }

  /**
   * Get documents for chat (with caching)
   */
  async getDocumentsForChat(sessionId, appId, authToken) {
    // Try to get from cache first
    const cachedSession = cacheService.getCachedDocuments(sessionId);

    if (cachedSession && cachedSession.appId === appId) {
      console.log(`📋 Using cached documents for session: ${sessionId}`);
      return cachedSession.documents;
    }

    // Fetch from User-Service
    console.log(`🔄 Fetching fresh documents from User-Service for appId: ${appId}`);
    const documents = await userService.getDocuments(appId, authToken);

    // Cache the documents
    cacheService.cacheDocuments(sessionId, appId, documents, authToken);

    return documents;
  }

  /**
   * Stream chat response
   */
  async streamChatResponse(res, query, context, sessionId) {
    try {
      // Set headers for streaming
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');

      // Send initial session info
      res.write(`data: ${JSON.stringify({
        type: 'session',
        sessionId,
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Stream response from OpenRouter
      const streamGenerator = openRouterService.generateStreamingResponse(query, context);

      for await (const chunk of streamGenerator) {
        res.write(`data: ${JSON.stringify({
          type: 'content',
          content: chunk
        })}\n\n`);
      }

      // Send completion signal
      res.write(`data: ${JSON.stringify({
        type: 'done',
        timestamp: new Date().toISOString()
      })}\n\n`);

      res.end();

    } catch (error) {
      console.error('❌ Streaming error:', error.message);

      if (!res.headersSent) {
        res.write(`data: ${JSON.stringify({
          type: 'error',
          message: 'Error during response generation'
        })}\n\n`);
      }

      res.end();
    }
  }

  /**
   * Get session info
   * GET /session/:sessionId
   */
  async getSession(req, res) {
    const { sessionId } = req.params;

    try {
      const session = cacheService.getCachedDocuments(sessionId);

      if (!session) {
        return res.status(404).json({
          error: true,
          message: 'Session not found or expired'
        });
      }

      res.json({
        error: false,
        session: {
          sessionId: session.sessionId,
          appId: session.appId,
          documentsCount: session.documents.length,
          timestamp: session.timestamp,
          lastAccessed: session.lastAccessed,
          accessCount: session.accessCount
        }
      });

    } catch (error) {
      console.error('❌ Get session error:', error.message);
      res.status(500).json({
        error: true,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Invalidate session
   * DELETE /session/:sessionId
   */
  async invalidateSession(req, res) {
    const { sessionId } = req.params;

    try {
      cacheService.invalidateSession(sessionId);

      res.json({
        error: false,
        message: 'Session invalidated successfully'
      });

    } catch (error) {
      console.error('❌ Invalidate session error:', error.message);
      res.status(500).json({
        error: true,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Get cache statistics
   * GET /stats
   */
  async getStats(req, res) {
    try {
      const stats = cacheService.getStats();

      res.json({
        error: false,
        stats
      });

    } catch (error) {
      console.error('❌ Get stats error:', error.message);
      res.status(500).json({
        error: true,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Widget chat endpoint (no authentication required)
   * GET /widget-chat/:appId
   */
  async widgetChat(req, res) {
    const { appId } = req.params;
    const { query, sessionId, stream = 'true', includeHistory = 'false' } = req.query;

    // Convert string query parameters to appropriate types
    const streamBool = stream === 'true' || stream === true;
    const includeHistoryBool = includeHistory === 'true' || includeHistory === true;

    try {
      // Basic validation
      if (!appId || !query) {
        return res.status(400).json({
          error: true,
          message: 'appId and query are required'
        });
      }

      if (query.length > 2000) {
        return res.status(400).json({
          error: true,
          message: 'Query is too long (max 2000 characters)'
        });
      }

      console.log(`💬 Widget chat request for appId: ${appId}, query: "${query.substring(0, 50)}..."`);

      // Step 1: Check API key validation results (if provided for widget)
      if (req.chatAiValidation) {
        const { creditInfo } = req.chatAiValidation;
        console.log(`💳 Widget credit info - Remaining: ${creditInfo.creditsRemaining}, Status: ${creditInfo.subscriptionStatus}, Can use: ${creditInfo.canUseService}`);

        // key-validator already checked credits, app validity, and permissions
        // No need for additional validation - key-validator is comprehensive
        console.log(`✅ Widget API key validation successful - App and credits validated by User-Service`);
      } else {
        // Step 2: Fallback validation when no API key provided for widget
        console.log(`🔍 No API key provided for widget - performing basic app validation`);
        const isValidApp = await userService.validateAppId(appId, null); // No auth token for widget

        if (!isValidApp) {
          return res.status(404).json({
            error: true,
            message: 'Application not found'
          });
        }

        console.log(`✅ Application validated for widget: ${appId}`);
      }

      // Step 3: Get or create session
      const currentSessionId = cacheService.getOrCreateSession(appId, sessionId);

      // Step 4: Get documents (cached or fetch from User-Service)
      const documents = await this.getDocumentsForChat(currentSessionId, appId, null); // No auth token

      if (!documents || documents.length === 0) {
        return res.status(404).json({
          error: true,
          message: 'No ready documents found for this application'
        });
      }

      // Step 4: Retrieve context from LlamaIndex
      const context = await llamaIndexService.retrieveFromMultipleDocuments(documents, query);

      // Step 5: Generate response
      if (streamBool) {
        await this.streamChatResponse(res, query, context, currentSessionId);
      } else {
        const response = await openRouterService.generateResponse(query, context);

        res.json({
          error: false,
          sessionId: currentSessionId,
          response,
          documentsUsed: documents.length,
          contextLength: context.length
        });
      }

    } catch (error) {
      console.error('❌ Widget chat error:', error.message);

      if (!res.headersSent) {
        res.status(500).json({
          error: true,
          message: 'Internal server error during chat processing'
        });
      }
    }
  }

  /**
   * Get embed URL for chat widget
   * GET /embed/:appId
   */
  async getEmbedUrl(req, res) {
    try {
      const { appId } = req.params;
      const { theme = 'light', width = '400px', height = '600px' } = req.query;

      console.log(`🔗 Generating embed URL for appId: ${appId}`);

      // Validate appId exists (no auth token required for embed)
      const isValidApp = await userService.validateAppId(appId, null);
      if (!isValidApp) {
        return res.status(404).json({
          error: true,
          message: 'Application not found'
        });
      }

      // Get the base URL from the request
      const protocol = req.protocol;
      const host = req.get('host');
      const baseUrl = `${protocol}://${host}`;

      // Generate iframe URL with parameters (no auth token needed for iframe)
      const iframeUrl = `${baseUrl}/chat-widget?appId=${appId}&theme=${theme}`;

      // Generate ready-to-use embed code
      const embedCode = `<iframe src="${iframeUrl}" width="${width}" height="${height}" frameborder="0" style="border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"></iframe>`;

      console.log(`✅ Generated embed URL: ${iframeUrl}`);

      res.json({
        error: false,
        iframeUrl,
        embedCode,
        appId,
        theme,
        dimensions: {
          width,
          height
        }
      });
    } catch (error) {
      console.error('❌ Error generating embed URL:', error);
      res.status(500).json({
        error: true,
        message: 'Failed to generate embed URL',
        details: error.message
      });
    }
  }

  /**
   * Serve chat widget HTML page
   * GET /chat-widget
   */
  async serveChatWidget(req, res) {
    try {
      const path = require('path');
      const fs = require('fs');

      const htmlPath = path.join(__dirname, '../../public/chat-widget.html');

      // Check if file exists
      if (!fs.existsSync(htmlPath)) {
        return res.status(404).json({
          error: true,
          message: 'Chat widget not found'
        });
      }

      // Set proper content type and send the HTML file
      res.setHeader('Content-Type', 'text/html');
      res.sendFile(htmlPath);

    } catch (error) {
      console.error('❌ Error serving chat widget:', error);
      res.status(500).json({
        error: true,
        message: 'Failed to serve chat widget'
      });
    }
  }

  /**
   * Health check endpoint
   * GET /health
   */
  async healthCheck(req, res) {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          llamaIndex: llamaIndexService.isConfigured,
          openRouter: openRouterService.isConfigured,
          cache: true
        },
        cache: cacheService.getStats()
      };

      res.json(health);

    } catch (error) {
      console.error('❌ Health check error:', error.message);
      res.status(500).json({
        status: 'unhealthy',
        error: error.message
      });
    }
  }
}

module.exports = new ChatController();
