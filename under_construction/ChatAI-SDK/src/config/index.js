require('dotenv').config();

const config = {
  // Server Configuration
  port: process.env.PORT || 3001,
  nodeEnv: process.env.NODE_ENV || 'development',

  // User Service Configuration
  userService: {
    url: process.env.USER_SERVICE_URL || 'http://localhost:3000',
  },

  // ChatAI Origin for key validation
  chatAiOrigin: process.env.CHATAI_ORIGIN || 'http://localhost:3001',

  // LlamaIndex Cloud Configuration
  llamaIndex: {
    apiKey: process.env.LLAMA_CLOUD_API_KEY || 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp',
    baseUrl: 'https://api.cloud.llamaindex.ai/api/v1',
  },

  // OpenRouter Configuration
  openRouter: {
    apiKey: process.env.OPENROUTER_API_KEY || '',
    baseUrl: 'https://openrouter.ai/api/v1',
    model: 'deepseek/deepseek-chat-v3-0324:free',
  },

  // Cache Configuration
  cache: {
    ttlMinutes: parseInt(process.env.CACHE_TTL_MINUTES) || 15,
    maxSessions: parseInt(process.env.MAX_SESSIONS) || 1000,
    cleanupIntervalMinutes: parseInt(process.env.CLEANUP_INTERVAL_MINUTES) || 5,
  },

  // Rate Limiting
  rateLimit: {
    windowMinutes: parseInt(process.env.RATE_LIMIT_WINDOW_MINUTES) || 15,
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  },

  // Logging
  logLevel: process.env.LOG_LEVEL || 'info',
};

// Validation
const requiredEnvVars = [
  'LLAMA_CLOUD_API_KEY',
  'OPENROUTER_API_KEY',
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars);
  process.exit(1);
}

module.exports = config;
