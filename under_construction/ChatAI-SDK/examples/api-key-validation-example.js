/**
 * ChatAI SDK API Key Validation Example
 * 
 * This example demonstrates how to use the ChatAI SDK with API key validation
 * and credit checking functionality.
 */

const fetch = require('node-fetch');

// Configuration
const SDK_BASE_URL = 'http://localhost:3001';
const USER_SERVICE_URL = 'http://localhost:3000';

// Example credentials (replace with actual values)
const EXAMPLE_CONFIG = {
  appId: 'your-app-id-uuid-here',
  apiKey: 'your-app-api-key-here',
  jwtToken: 'your-jwt-token-here',
  query: 'What is the main topic of the uploaded documents?'
};

/**
 * Example 1: Chat with API Key Validation
 */
async function chatWithApiKeyValidation() {
  console.log('\n🔑 Example 1: Chat with API Key Validation');
  console.log('=' .repeat(50));

  try {
    const response = await fetch(
      `${SDK_BASE_URL}/chat/${EXAMPLE_CONFIG.appId}?` + 
      `apiKey=${EXAMPLE_CONFIG.apiKey}&` +
      `query=${encodeURIComponent(EXAMPLE_CONFIG.query)}&` +
      `stream=false`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${EXAMPLE_CONFIG.jwtToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const result = await response.json();

    if (result.error) {
      console.error('❌ Chat request failed:', result.message);
      if (result.creditInfo) {
        console.log('💳 Credit Info:', result.creditInfo);
      }
    } else {
      console.log('✅ Chat successful!');
      console.log('📝 Response:', result.result?.response?.substring(0, 100) + '...');
      if (result.result?.creditInfo) {
        console.log('💳 Credit Info:', result.result.creditInfo);
      }
    }

  } catch (error) {
    console.error('❌ Request error:', error.message);
  }
}

/**
 * Example 2: Widget Chat with API Key (Optional)
 */
async function widgetChatWithApiKey() {
  console.log('\n🖥️  Example 2: Widget Chat with API Key');
  console.log('=' .repeat(50));

  try {
    const response = await fetch(
      `${SDK_BASE_URL}/widget/chat/${EXAMPLE_CONFIG.appId}?` + 
      `apiKey=${EXAMPLE_CONFIG.apiKey}&` +
      `query=${encodeURIComponent(EXAMPLE_CONFIG.query)}&` +
      `stream=false`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
          // Note: No Authorization header needed for widget
        }
      }
    );

    const result = await response.json();

    if (result.error) {
      console.error('❌ Widget chat failed:', result.message);
      if (result.creditInfo) {
        console.log('💳 Credit Info:', result.creditInfo);
      }
    } else {
      console.log('✅ Widget chat successful!');
      console.log('📝 Response:', result.result?.response?.substring(0, 100) + '...');
      if (result.result?.creditInfo) {
        console.log('💳 Credit Info:', result.result.creditInfo);
      }
    }

  } catch (error) {
    console.error('❌ Widget request error:', error.message);
  }
}

/**
 * Example 3: Chat without API Key (No Credit Validation)
 */
async function chatWithoutApiKey() {
  console.log('\n🚫 Example 3: Chat without API Key');
  console.log('=' .repeat(50));

  try {
    const response = await fetch(
      `${SDK_BASE_URL}/chat/${EXAMPLE_CONFIG.appId}?` + 
      `query=${encodeURIComponent(EXAMPLE_CONFIG.query)}&` +
      `stream=false`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${EXAMPLE_CONFIG.jwtToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const result = await response.json();

    if (result.error) {
      console.error('❌ Chat without API key failed:', result.message);
    } else {
      console.log('✅ Chat without API key successful!');
      console.log('📝 Response:', result.result?.response?.substring(0, 100) + '...');
      console.log('⚠️  No credit validation performed');
    }

  } catch (error) {
    console.error('❌ Request error:', error.message);
  }
}

/**
 * Example 4: Check Service Health
 */
async function checkServiceHealth() {
  console.log('\n🏥 Example 4: Service Health Check');
  console.log('=' .repeat(50));

  try {
    const response = await fetch(`${SDK_BASE_URL}/health`);
    const health = await response.json();

    console.log('📊 Service Status:', health.status);
    console.log('🔧 Services:');
    console.log('   - LlamaIndex:', health.services?.llamaIndex ? '✅' : '❌');
    console.log('   - OpenRouter:', health.services?.openRouter ? '✅' : '❌');
    console.log('   - Cache:', health.services?.cache ? '✅' : '❌');

  } catch (error) {
    console.error('❌ Health check error:', error.message);
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 ChatAI SDK API Key Validation Examples');
  console.log('=' .repeat(60));
  
  // Validate configuration
  if (!EXAMPLE_CONFIG.appId || EXAMPLE_CONFIG.appId === 'your-app-id-uuid-here') {
    console.error('❌ Please update EXAMPLE_CONFIG with your actual appId');
    return;
  }

  if (!EXAMPLE_CONFIG.apiKey || EXAMPLE_CONFIG.apiKey === 'your-app-api-key-here') {
    console.error('❌ Please update EXAMPLE_CONFIG with your actual API key');
    return;
  }

  if (!EXAMPLE_CONFIG.jwtToken || EXAMPLE_CONFIG.jwtToken === 'your-jwt-token-here') {
    console.error('❌ Please update EXAMPLE_CONFIG with your actual JWT token');
    return;
  }

  // Run examples
  await checkServiceHealth();
  await chatWithApiKeyValidation();
  await widgetChatWithApiKey();
  await chatWithoutApiKey();

  console.log('\n✅ All examples completed!');
  console.log('\n📝 Notes:');
  console.log('   - API key validation enables credit checking');
  console.log('   - Free users have limited credits');
  console.log('   - Pro/Enterprise users have unlimited credits');
  console.log('   - Widget requests can optionally use API keys');
}

// Run examples if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  chatWithApiKeyValidation,
  widgetChatWithApiKey,
  chatWithoutApiKey,
  checkServiceHealth
};
