{"name": "chatai-sdk-service", "version": "1.0.0", "description": "Express SDK service for ChatAI with LlamaIndex and OpenRouter integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "node-fetch": "^2.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "keywords": ["chatai", "sdk", "llamaindex", "openrouter", "rag", "express"], "author": "ChatAI Team", "license": "MIT"}