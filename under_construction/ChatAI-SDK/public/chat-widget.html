<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatAI Widget</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--bg-color);
            color: var(--text-color);
        }

        /* Light theme */
        .theme-light {
            --bg-color: #ffffff;
            --text-color: #333333;
            --border-color: #e1e5e9;
            --input-bg: #f8f9fa;
            --button-bg: #007bff;
            --button-text: #ffffff;
            --user-msg-bg: #007bff;
            --user-msg-text: #ffffff;
            --bot-msg-bg: #f1f3f4;
            --bot-msg-text: #333333;
        }

        /* Dark theme */
        .theme-dark {
            --bg-color: #1a1a1a;
            --text-color: #ffffff;
            --border-color: #404040;
            --input-bg: #2d2d2d;
            --button-bg: #0d6efd;
            --button-text: #ffffff;
            --user-msg-bg: #0d6efd;
            --user-msg-text: #ffffff;
            --bot-msg-bg: #2d2d2d;
            --bot-msg-text: #ffffff;
        }

        .chat-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-color);
            text-align: center;
            font-weight: 600;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user {
            align-self: flex-end;
            background: var(--user-msg-bg);
            color: var(--user-msg-text);
        }

        .message.bot {
            align-self: flex-start;
            background: var(--bot-msg-bg);
            color: var(--bot-msg-text);
        }

        .message.typing {
            background: var(--bot-msg-bg);
            color: var(--bot-msg-text);
            align-self: flex-start;
        }

        .typing-indicator {
            display: inline-flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--text-color);
            opacity: 0.4;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {

            0%,
            60%,
            100% {
                opacity: 0.4;
            }

            30% {
                opacity: 1;
            }
        }

        .chat-input {
            padding: 16px;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 8px;
        }

        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 24px;
            background: var(--input-bg);
            color: var(--text-color);
            outline: none;
            font-size: 14px;
        }

        .input-field:focus {
            border-color: var(--button-bg);
        }

        .send-button {
            padding: 12px 20px;
            background: var(--button-bg);
            color: var(--button-text);
            border: none;
            border-radius: 24px;
            cursor: pointer;
            font-weight: 500;
            transition: opacity 0.2s;
        }

        .send-button:hover:not(:disabled) {
            opacity: 0.9;
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .error-message {
            background: #dc3545;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 8px 16px;
            font-size: 14px;
        }

        .welcome-message {
            text-align: center;
            color: var(--text-color);
            opacity: 0.7;
            padding: 20px;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="chat-header">
        💬 ChatAI Assistant
    </div>

    <div class="chat-messages" id="chatMessages">
        <div class="welcome-message">
            👋 Hello! I'm here to help you with questions about your documents. Ask me anything!
        </div>
    </div>

    <div class="chat-input">
        <input type="text" class="input-field" id="messageInput" placeholder="Type your message..." maxlength="500">
        <button class="send-button" id="sendButton">Send</button>
    </div>

    <script>
        class ChatWidget {
            constructor() {
                this.appId = this.getUrlParam('appId');
                this.theme = this.getUrlParam('theme') || 'light';
                this.sessionId = null;
                this.isLoading = false;

                this.initializeTheme();
                this.initializeElements();
                this.bindEvents();

                if (!this.appId) {
                    this.showError('Application ID is required');
                }
            }

            getUrlParam(param) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(param);
            }

            initializeTheme() {
                document.body.className = `theme-${this.theme}`;
            }

            initializeElements() {
                this.messagesContainer = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
            }

            bindEvents() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isLoading) return;

                this.addMessage(message, 'user');
                this.messageInput.value = '';
                this.setLoading(true);

                try {
                    const response = await this.callChatAPI(message);
                    this.handleResponse(response);
                } catch (error) {
                    this.showError('Failed to send message. Please try again.');
                    console.error('Chat error:', error);
                } finally {
                    this.setLoading(false);
                }
            }

            async callChatAPI(message) {
                const url = `/widget-chat/${this.appId}?query=${encodeURIComponent(message)}&stream=false&includeHistory=false`;

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                return await response.json();
            }

            handleResponse(data) {
                if (data.error) {
                    this.showError(data.message || 'An error occurred');
                    return;
                }

                this.sessionId = data.sessionId;
                this.addMessage(data.response, 'bot');
            }

            addMessage(content, type) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.textContent = content;

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }

            setLoading(loading) {
                this.isLoading = loading;
                this.sendButton.disabled = loading;

                if (loading) {
                    this.addTypingIndicator();
                } else {
                    this.removeTypingIndicator();
                }
            }

            addTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'message typing';
                typingDiv.id = 'typingIndicator';
                typingDiv.innerHTML = `
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `;

                this.messagesContainer.appendChild(typingDiv);
                this.scrollToBottom();
            }

            removeTypingIndicator() {
                const typingIndicator = document.getElementById('typingIndicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            showError(message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = message;

                this.messagesContainer.appendChild(errorDiv);
                this.scrollToBottom();

                // Remove error after 5 seconds
                setTimeout(() => errorDiv.remove(), 5000);
            }

            scrollToBottom() {
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
        }

        // Initialize the chat widget when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ChatWidget();
        });
    </script>
</body>

</html>