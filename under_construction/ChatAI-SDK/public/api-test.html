<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatAI SDK API Tester</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #3498db;
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .btn:hover:not(:disabled) {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: #95a5a6;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #7f8c8d;
        }

        .response-container {
            margin-top: 20px;
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .status-loading {
            background: #fff3cd;
            color: #856404;
        }

        .response-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .timing-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .timing-card {
            background: #ecf0f1;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }

        .timing-value {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .timing-label {
            font-size: 12px;
            color: #7f8c8d;
            text-transform: uppercase;
        }

        .cache-status {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .cache-indicator {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }

        .cache-hit {
            background: #d4edda;
            color: #155724;
        }

        .cache-miss {
            background: #f8d7da;
            color: #721c24;
        }

        .stream-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
        }

        .stream-chunk {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .stream-chunk.content {
            color: #2c3e50;
        }

        .stream-chunk.meta {
            color: #7f8c8d;
            font-style: italic;
        }

        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .quick-test-btn {
            padding: 8px 12px;
            font-size: 12px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .quick-test-btn:hover {
            background: #c0392b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ChatAI SDK API Tester</h1>
            <p>Test your enhanced caching system with real-time performance monitoring</p>
        </div>

        <!-- API Configuration -->
        <div class="test-section">
            <div class="section-title">⚙️ API Configuration</div>
            <div class="form-group">
                <label for="apiKey">API Key:</label>
                <input type="text" id="apiKey" value="test_api_key_1751884336144_vp9gospvg" placeholder="Enter your API key">
            </div>
            <div class="form-group">
                <label for="baseUrl">Base URL:</label>
                <input type="text" id="baseUrl" value="http://localhost:3001" placeholder="http://localhost:3001">
            </div>
        </div>

        <!-- Query Testing -->
        <div class="test-section">
            <div class="section-title">💬 Query Testing</div>
            <div class="form-group">
                <label for="query">Your Question:</label>
                <textarea id="query" placeholder="What is the invoice amount?">invoice amount</textarea>
            </div>
            <div class="form-group">
                <label for="sessionId">Session ID (optional):</label>
                <input type="text" id="sessionId" placeholder="Leave empty for auto-generation">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="streamMode" checked> Enable Streaming
                </label>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="includeHistory"> Include History
                </label>
            </div>
            
            <button class="btn" id="sendQuery" onclick="sendQuery()">🚀 Send Query</button>
            <button class="btn btn-secondary" onclick="clearResponse()">🗑️ Clear</button>
            
            <!-- Quick Test Buttons -->
            <div class="quick-tests">
                <button class="quick-test-btn" onclick="quickTest('What is the invoice amount?')">Invoice Amount</button>
                <button class="quick-test-btn" onclick="quickTest('Show me the total cost')">Total Cost</button>
                <button class="quick-test-btn" onclick="quickTest('When is the due date?')">Due Date</button>
                <button class="quick-test-btn" onclick="quickTest('What is the payment deadline?')">Payment Deadline</button>
                <button class="quick-test-btn" onclick="quickTest('Show me contact details')">Contact Info</button>
                <button class="quick-test-btn" onclick="quickTest('What items are included?')">Item Details</button>
            </div>
        </div>

        <!-- Response Display -->
        <div class="test-section">
            <div class="section-title">📊 Response & Performance</div>
            <div class="response-container">
                <div class="response-header">
                    <span>Response Status:</span>
                    <span class="status-badge" id="statusBadge">Ready</span>
                </div>
                <div class="response-content" id="responseContent">Ready to test your API...</div>
                
                <!-- Timing Information -->
                <div class="timing-info" id="timingInfo" style="display: none;">
                    <div class="timing-card">
                        <div class="timing-value" id="totalTime">-</div>
                        <div class="timing-label">Total Time</div>
                    </div>
                    <div class="timing-card">
                        <div class="timing-value" id="firstChunk">-</div>
                        <div class="timing-label">First Chunk</div>
                    </div>
                    <div class="timing-card">
                        <div class="timing-value" id="streamTime">-</div>
                        <div class="timing-label">Stream Time</div>
                    </div>
                </div>

                <!-- Cache Status -->
                <div class="cache-status" id="cacheStatus" style="display: none;">
                    <div class="cache-indicator" id="apiKeyCache">API Key: Unknown</div>
                    <div class="cache-indicator" id="contextCache">Context: Unknown</div>
                </div>
            </div>
        </div>

        <!-- Streaming Display -->
        <div class="test-section" id="streamSection" style="display: none;">
            <div class="section-title">🌊 Live Stream</div>
            <div class="stream-container" id="streamContainer"></div>
        </div>

        <!-- Cache Statistics -->
        <div class="test-section">
            <div class="section-title">📈 Cache Statistics</div>
            <button class="btn btn-secondary" onclick="loadCacheStats()">🔄 Refresh Stats</button>
            <div class="response-content" id="cacheStats">Click "Refresh Stats" to load cache information...</div>
        </div>
    </div>

    <script>
        let currentSessionId = null;
        let streamController = null;

        function updateStatus(status, type = 'loading') {
            const badge = document.getElementById('statusBadge');
            badge.textContent = status;
            badge.className = `status-badge status-${type}`;
        }

        function updateResponse(content) {
            document.getElementById('responseContent').textContent = content;
        }

        function quickTest(query) {
            document.getElementById('query').value = query;
            sendQuery();
        }

        function clearResponse() {
            updateResponse('Ready to test your API...');
            updateStatus('Ready', 'loading');
            document.getElementById('timingInfo').style.display = 'none';
            document.getElementById('cacheStatus').style.display = 'none';
            document.getElementById('streamSection').style.display = 'none';
        }

        async function sendQuery() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const baseUrl = document.getElementById('baseUrl').value.trim();
            const query = document.getElementById('query').value.trim();
            const sessionId = document.getElementById('sessionId').value.trim();
            const streamMode = document.getElementById('streamMode').checked;
            const includeHistory = document.getElementById('includeHistory').checked;

            if (!apiKey || !query) {
                alert('Please enter both API key and query');
                return;
            }

            // Build URL
            const params = new URLSearchParams({
                apikey: apiKey,
                query: query,
                stream: streamMode.toString(),
                includeHistory: includeHistory.toString()
            });

            if (sessionId) {
                params.append('sessionId', sessionId);
            }

            const url = `${baseUrl}/api/v1/?${params.toString()}`;

            updateStatus('Sending...', 'loading');
            updateResponse('Sending request...');

            const startTime = Date.now();

            try {
                if (streamMode) {
                    await handleStreamingResponse(url, startTime);
                } else {
                    await handleRegularResponse(url, startTime);
                }
            } catch (error) {
                updateStatus('Error', 'error');
                updateResponse(`Error: ${error.message}`);
                console.error('API Error:', error);
            }
        }

        async function handleRegularResponse(url, startTime) {
            const response = await fetch(url, {
                headers: {
                    'Origin': document.getElementById('baseUrl').value
                }
            });

            const endTime = Date.now();
            const totalTime = endTime - startTime;

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.error) {
                updateStatus('Error', 'error');
                updateResponse(`API Error: ${data.message}`);
                return;
            }

            updateStatus('Success', 'success');
            updateResponse(JSON.stringify(data, null, 2));

            // Update timing info
            if (data.timing) {
                showTimingInfo(data.timing, totalTime);
            }

            // Update cache status
            if (data.cached) {
                showCacheStatus(data.cached);
            }

            currentSessionId = data.sessionId;
        }

        async function handleStreamingResponse(url, startTime) {
            document.getElementById('streamSection').style.display = 'block';
            const streamContainer = document.getElementById('streamContainer');
            streamContainer.innerHTML = '';

            let fullResponse = '';
            let firstChunkTime = null;
            let streamEndTime = null;

            const response = await fetch(url, {
                headers: {
                    'Origin': document.getElementById('baseUrl').value
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            updateStatus('Streaming...', 'loading');

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                if (data.type === 'content') {
                                    if (!firstChunkTime) {
                                        firstChunkTime = Date.now();
                                    }
                                    fullResponse += data.content;
                                    addStreamChunk(data.content, 'content');
                                } else if (data.type === 'session') {
                                    currentSessionId = data.sessionId;
                                    addStreamChunk(`Session: ${data.sessionId}`, 'meta');
                                } else if (data.type === 'done') {
                                    streamEndTime = Date.now();
                                    addStreamChunk('Stream completed', 'meta');
                                    
                                    if (data.timing) {
                                        showTimingInfo(data.timing, streamEndTime - startTime);
                                    }
                                } else if (data.type === 'error') {
                                    addStreamChunk(`Error: ${data.message}`, 'meta');
                                }
                            } catch (e) {
                                console.warn('Failed to parse SSE data:', line);
                            }
                        }
                    }
                }

                updateStatus('Success', 'success');
                updateResponse(fullResponse || 'Stream completed successfully');

            } catch (error) {
                updateStatus('Stream Error', 'error');
                addStreamChunk(`Stream error: ${error.message}`, 'meta');
                throw error;
            }
        }

        function addStreamChunk(content, type) {
            const streamContainer = document.getElementById('streamContainer');
            const chunkDiv = document.createElement('div');
            chunkDiv.className = `stream-chunk ${type}`;
            chunkDiv.textContent = content;
            streamContainer.appendChild(chunkDiv);
            streamContainer.scrollTop = streamContainer.scrollHeight;
        }

        function showTimingInfo(timing, totalTime) {
            document.getElementById('timingInfo').style.display = 'grid';
            document.getElementById('totalTime').textContent = `${totalTime}ms`;
            document.getElementById('firstChunk').textContent = timing.timeToFirstChunk ? `${timing.timeToFirstChunk}ms` : '-';
            document.getElementById('streamTime').textContent = timing.openRouterStream ? `${timing.openRouterStream}ms` : '-';
        }

        function showCacheStatus(cached) {
            document.getElementById('cacheStatus').style.display = 'flex';
            
            const apiKeyCache = document.getElementById('apiKeyCache');
            const contextCache = document.getElementById('contextCache');
            
            apiKeyCache.textContent = `API Key: ${cached.apiKey ? 'HIT' : 'MISS'}`;
            apiKeyCache.className = `cache-indicator ${cached.apiKey ? 'cache-hit' : 'cache-miss'}`;
            
            contextCache.textContent = `Context: ${cached.context ? 'HIT' : 'MISS'}`;
            contextCache.className = `cache-indicator ${cached.context ? 'cache-hit' : 'cache-miss'}`;
        }

        async function loadCacheStats() {
            try {
                const baseUrl = document.getElementById('baseUrl').value.trim();
                const response = await fetch(`${baseUrl}/stats`);
                const data = await response.json();
                
                document.getElementById('cacheStats').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('cacheStats').textContent = `Error loading stats: ${error.message}`;
            }
        }

        // Load cache stats on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadCacheStats();
        });
    </script>
</body>
</html>
