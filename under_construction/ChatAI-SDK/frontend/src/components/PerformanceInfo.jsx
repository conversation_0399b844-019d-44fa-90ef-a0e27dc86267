import React from 'react';

const PerformanceInfo = ({ timing, totalTime, firstChunkTime }) => {
  return (
    <div className="performance-info">
      <div className="performance-metrics">
        ⏱️ {totalTime}ms total
        {firstChunkTime && (
          <span className="metric"> • First chunk: {firstChunkTime}ms</span>
        )}
        {timing && timing.processing && (
          <span className="metric"> • Processing: {timing.processing}ms</span>
        )}
      </div>
      
      {timing && timing.cache && (
        <div className="cache-info">
          {timing.cache.apiKey && (
            <span className="cache-badge cache-hit">API ✓</span>
          )}
          {!timing.cache.apiKey && (
            <span className="cache-badge cache-miss">API ✗</span>
          )}
          
          {timing.cache.context && (
            <span className="cache-badge cache-hit">Context ✓</span>
          )}
          {!timing.cache.context && (
            <span className="cache-badge cache-miss">Context ✗</span>
          )}
        </div>
      )}
    </div>
  );
};

export default PerformanceInfo;
