import React from 'react';
import PerformanceInfo from './PerformanceInfo';

const Message = ({ message }) => {
  const { content, type, isStreaming, timing, totalTime, firstChunkTime } = message;

  if (type === 'performance') {
    return (
      <PerformanceInfo 
        timing={timing}
        totalTime={totalTime}
        firstChunkTime={firstChunkTime}
      />
    );
  }

  const getMessageClass = () => {
    let baseClass = 'message';
    
    switch (type) {
      case 'user':
        return `${baseClass} message-user`;
      case 'bot':
        return `${baseClass} message-bot ${isStreaming ? 'streaming' : ''}`;
      case 'error':
        return `${baseClass} message-error`;
      default:
        return baseClass;
    }
  };

  const formatContent = (text) => {
    // Handle line breaks and basic formatting
    return text.split('\n').map((line, index) => (
      <React.Fragment key={index}>
        {line}
        {index < text.split('\n').length - 1 && <br />}
      </React.Fragment>
    ));
  };

  return (
    <div className={getMessageClass()}>
      <div className="message-content">
        {formatContent(content)}
        {isStreaming && <span className="streaming-cursor">|</span>}
      </div>
      {type === 'user' && (
        <div className="message-timestamp">
          {message.timestamp?.toLocaleTimeString()}
        </div>
      )}
    </div>
  );
};

export default Message;
