{"name": "rag-backend-service", "version": "1.0.0", "description": "Complete RAG Backend Service with React Frontend - Document processing and vector search", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "node test/test-upload.js", "frontend:install": "cd frontend && npm install", "frontend:dev": "cd frontend && npm run dev", "frontend:build": "cd frontend && npm run build", "dev:all": "concurrently \"npm run dev\" \"npm run frontend:dev\"", "install:all": "npm install && npm run frontend:install", "build:all": "npm run frontend:build"}, "keywords": ["rag", "llamaparse", "llamacloud", "document-processing", "vector-search", "react", "frontend", "backend", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "sqlite3": "^5.1.6", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}}