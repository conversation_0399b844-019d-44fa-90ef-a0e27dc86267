import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Test the complete RAG backend service
 */
async function testRAGBackend() {
  console.log('🧪 Testing RAG Backend Service\n');

  const baseUrl = 'http://localhost:3000';
  let documentId = null;

  try {
    // Step 1: Health check
    console.log('1️⃣ Health check...');
    const healthResponse = await fetch(`${baseUrl}/health`);
    
    if (!healthResponse.ok) {
      throw new Error('Service is not healthy');
    }
    
    const healthData = await healthResponse.json();
    console.log(`   ✅ Service healthy: ${healthData.service} v${healthData.version}`);

    // Step 2: Create test document
    console.log('\n2️⃣ Creating test document...');
    const testContent = `
# Test Document for RAG Processing

This is a comprehensive test document to verify the RAG backend service functionality.

## Introduction
This document contains various sections to test document parsing and vector indexing capabilities.

## Main Content
The RAG (Retrieval-Augmented Generation) system processes documents through multiple stages:
1. Document upload and validation
2. Text extraction using LlamaParse
3. Vector embedding creation with LlamaCloud
4. Semantic search capabilities

## Technical Details
The system supports various file formats including PDF, Word documents, and text files.
It implements security measures such as file validation, rate limiting, and error sanitization.

## Conclusion
This test document should be successfully processed and made available for semantic search queries.
    `.trim();

    const testFilePath = path.join(__dirname, 'test-document.txt');
    fs.writeFileSync(testFilePath, testContent);
    console.log(`   ✅ Test document created: ${testFilePath}`);

    // Step 3: Upload document
    console.log('\n3️⃣ Uploading document...');
    const formData = new FormData();
    const fileBuffer = fs.readFileSync(testFilePath);
    const blob = new Blob([fileBuffer], { type: 'text/plain' });
    formData.append('file', blob, 'test-document.txt');

    const uploadResponse = await fetch(`${baseUrl}/api/documents/upload`, {
      method: 'POST',
      body: formData
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      throw new Error(`Upload failed: ${uploadResponse.status} - ${errorText}`);
    }

    const uploadData = await uploadResponse.json();
    documentId = uploadData.document.id;
    console.log(`   ✅ Document uploaded successfully`);
    console.log(`   📄 Document ID: ${documentId}`);
    console.log(`   📊 Status: ${uploadData.document.status}`);

    // Step 4: Monitor processing
    console.log('\n4️⃣ Monitoring document processing...');
    let attempts = 0;
    const maxAttempts = 20; // 2 minutes max
    let document = null;

    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 6000)); // Wait 6 seconds

      const statusResponse = await fetch(`${baseUrl}/api/documents/${documentId}`);
      
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        document = statusData.document;
        
        console.log(`   🔄 Attempt ${attempts + 1}: Status = ${document.status}`);

        if (document.status === 'ready') {
          console.log('   ✅ Document processing completed!');
          console.log(`   📊 Pages: ${document.metadata?.page_count || 'N/A'}`);
          console.log(`   📊 Words: ${document.metadata?.word_count || 'N/A'}`);
          console.log(`   🎯 Index ID: ${document.indexId}`);
          break;
        } else if (document.status === 'error') {
          console.log('   ❌ Document processing failed');
          console.log(`   📋 Error: ${document.errorMessage}`);
          break;
        }
      }

      attempts++;
    }

    if (attempts >= maxAttempts && document?.status !== 'ready') {
      console.log('   ⚠️  Processing timeout - still in progress');
    }

    // Step 5: Test document listing
    console.log('\n5️⃣ Testing document listing...');
    const listResponse = await fetch(`${baseUrl}/api/documents`);
    
    if (listResponse.ok) {
      const listData = await listResponse.json();
      console.log(`   ✅ Found ${listData.documents.length} documents`);
      
      if (listData.documents.length > 0) {
        const doc = listData.documents.find(d => d.id === documentId);
        if (doc) {
          console.log(`   📄 Test document: ${doc.filename} (${doc.status})`);
        }
      }
    }

    // Step 6: Test querying (only if document is ready)
    if (document?.status === 'ready') {
      console.log('\n6️⃣ Testing document querying...');
      
      const queries = [
        "What is RAG?",
        "What file formats are supported?",
        "What are the main processing stages?"
      ];

      for (const query of queries) {
        try {
          const queryResponse = await fetch(`${baseUrl}/api/documents/${documentId}/query`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query, topK: 3 })
          });

          if (queryResponse.ok) {
            const queryData = await queryResponse.json();
            console.log(`   ✅ Query: "${query}"`);
            console.log(`   📊 Results: ${queryData.results.length} chunks found`);
            
            if (queryData.results.length > 0) {
              const topResult = queryData.results[0];
              console.log(`   🎯 Top result (score: ${topResult.score?.toFixed(2) || 'N/A'}): ${topResult.text.substring(0, 100)}...`);
            }
          } else {
            console.log(`   ❌ Query failed: ${query}`);
          }
        } catch (queryError) {
          console.log(`   ❌ Query error: ${queryError.message}`);
        }
      }
    }

    // Step 7: Test document retrieval
    console.log('\n7️⃣ Testing document retrieval...');
    const getResponse = await fetch(`${baseUrl}/api/documents/${documentId}`);
    
    if (getResponse.ok) {
      const getData = await getResponse.json();
      console.log(`   ✅ Document retrieved successfully`);
      console.log(`   📄 Filename: ${getData.document.filename}`);
      console.log(`   📊 Size: ${getData.document.size} bytes`);
      console.log(`   🔄 Status: ${getData.document.status}`);
    }

    console.log('\n🎉 RAG Backend Service Test Completed!');

    // Test Summary
    console.log('\n📋 Test Summary:');
    console.log('✅ Health check');
    console.log('✅ Document upload with validation');
    console.log('✅ Background document processing');
    console.log('✅ Document status monitoring');
    console.log('✅ Document listing');
    console.log('✅ Document retrieval');
    
    if (document?.status === 'ready') {
      console.log('✅ Vector search querying');
      console.log('✅ LlamaParse integration');
      console.log('✅ LlamaCloud indexing');
    } else {
      console.log('⚠️  Document processing incomplete (check API keys)');
    }

    // Cleanup
    console.log('\n🧹 Cleanup...');
    try {
      fs.unlinkSync(testFilePath);
      console.log('   ✅ Test file cleaned up');
    } catch (cleanupError) {
      console.log('   ⚠️  Cleanup warning:', cleanupError.message);
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    // Cleanup on error
    try {
      const testFilePath = path.join(__dirname, 'test-document.txt');
      if (fs.existsSync(testFilePath)) {
        fs.unlinkSync(testFilePath);
      }
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
  }
}

// Run the test
testRAGBackend().catch(console.error);
