import multer from 'multer';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

/**
 * File validation with magic number checking
 */
function validateFileContent(buffer, mimetype, filename) {
  const firstBytes = buffer.slice(0, 4);
  
  // Magic number validation for common file types
  const magicNumbers = {
    'application/pdf': [[0x25, 0x50, 0x44, 0x46]], // %PDF
    'application/msword': [[0xD0, 0xCF, 0x11, 0xE0]], // MS Office
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
      [0x50, 0x4B, 0x03, 0x04], // ZIP-based formats
      [0x50, 0x4B, 0x05, 0x06],
      [0x50, 0x4B, 0x07, 0x08]
    ],
    'text/plain': [], // Text files don't have magic numbers
    'text/markdown': [],
    'text/html': [],
    'application/rtf': [[0x7B, 0x5C, 0x72, 0x74]] // {\rt
  };
  
  const expectedMagicNumbers = magicNumbers[mimetype];
  
  // If no magic numbers defined for this type, allow it
  if (!expectedMagicNumbers || expectedMagicNumbers.length === 0) {
    return true;
  }
  
  // Check if file starts with any of the expected magic numbers
  return expectedMagicNumbers.some(magic => {
    return magic.every((byte, index) => 
      index < firstBytes.length && firstBytes[index] === byte
    );
  });
}

/**
 * Configure multer for file uploads with enhanced security
 */
const storage = multer.memoryStorage();

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 5 * 1024 * 1024, // 5MB default
    files: parseInt(process.env.UPLOAD_MAX_FILES) || 10, // 10 files max
    fields: 10, // Limit form fields
  },
  fileFilter: (req, file, cb) => {
    // Allowed file types
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv',
      'text/html',
      'text/markdown',
      'application/rtf',
      'application/xml',
      'text/xml'
    ];
    
    // Allowed file extensions
    const fileExtension = file.originalname.toLowerCase().split('.').pop();
    const allowedExtensions = [
      'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
      'txt', 'csv', 'html', 'htm', 'md', 'rtf', 'xml'
    ];
    
    // Check MIME type and file extension
    if (!allowedTypes.includes(file.mimetype) || !allowedExtensions.includes(fileExtension || '')) {
      return cb(new Error('Unsupported file type. Please upload documents only: PDF, Word, PowerPoint, Excel, or text files.'));
    }
    
    // Check for path traversal attempts
    if (file.originalname.includes('../') || file.originalname.includes('..\\')) {
      return cb(new Error('Invalid filename detected.'));
    }
    
    // Check filename length
    if (file.originalname.length > 255) {
      return cb(new Error('Filename too long. Maximum 255 characters allowed.'));
    }
    
    cb(null, true);
  },
});

/**
 * Enhanced file validation middleware
 */
export function validateUploadedFile(req, res, next) {
  if (!req.file) {
    return res.status(400).json({
      error: 'No file uploaded',
      message: 'Please select a file to upload'
    });
  }

  try {
    // Validate file content with magic numbers
    if (!validateFileContent(req.file.buffer, req.file.mimetype, req.file.originalname)) {
      return res.status(400).json({
        error: 'Invalid file content',
        message: 'File content does not match the file type. Possible malicious file detected.'
      });
    }

    // Additional security checks
    if (req.file.size === 0) {
      return res.status(400).json({
        error: 'Empty file',
        message: 'The uploaded file is empty'
      });
    }

    // Check for suspicious file patterns
    const suspiciousPatterns = [
      /\.exe$/i,
      /\.bat$/i,
      /\.cmd$/i,
      /\.scr$/i,
      /\.vbs$/i,
      /\.js$/i,
      /\.jar$/i
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(req.file.originalname))) {
      return res.status(400).json({
        error: 'Suspicious file type',
        message: 'This file type is not allowed for security reasons'
      });
    }

    next();
  } catch (error) {
    console.error('File validation error:', error);
    res.status(500).json({
      error: 'File validation failed',
      message: 'Unable to validate the uploaded file'
    });
  }
}

/**
 * Rate limiting for uploads (per IP)
 */
const uploadAttempts = new Map();

export function uploadRateLimit(req, res, next) {
  const clientIP = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  const windowMs = 60 * 60 * 1000; // 1 hour
  const maxUploads = 20; // 20 uploads per hour per IP

  if (!uploadAttempts.has(clientIP)) {
    uploadAttempts.set(clientIP, []);
  }

  const attempts = uploadAttempts.get(clientIP);
  
  // Clean old attempts
  const recentAttempts = attempts.filter(timestamp => now - timestamp < windowMs);
  
  if (recentAttempts.length >= maxUploads) {
    return res.status(429).json({
      error: 'Upload rate limit exceeded',
      message: 'Too many uploads from this IP. Please try again later.',
      retryAfter: Math.ceil((recentAttempts[0] + windowMs - now) / 1000)
    });
  }

  // Track this attempt
  recentAttempts.push(now);
  uploadAttempts.set(clientIP, recentAttempts);

  next();
}

export default upload;
