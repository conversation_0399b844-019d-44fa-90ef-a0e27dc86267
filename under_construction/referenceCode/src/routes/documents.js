import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import upload, { validateUploadedFile, uploadRateLimit } from '../middleware/upload.js';
import { DocumentModel } from '../models/database.js';
import { llamaParseService } from '../services/llamaparse.js';
import { llamaIndexService } from '../services/llamaindex.js';

const router = express.Router();

/**
 * Upload and process document
 * POST /api/documents/upload
 */
router.post('/upload', 
  uploadRateLimit,
  upload.single('file'),
  validateUploadedFile,
  async (req, res) => {
    const documentId = uuidv4();
    
    try {
      console.log(`📤 Starting upload process for: ${req.file.originalname}`);
      
      // Create document record
      const documentData = {
        id: documentId,
        filename: `${documentId}_${req.file.originalname}`,
        originalName: req.file.originalname,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        status: 'uploading'
      };

      const document = await DocumentModel.create(documentData);
      
      // Return immediate response
      res.status(201).json({
        success: true,
        message: 'File uploaded successfully. Processing started.',
        document: {
          id: document.id,
          filename: document.originalName,
          size: document.fileSize,
          status: document.status,
          created_at: new Date().toISOString()
        }
      });

      // Start background processing
      processDocumentAsync(documentId, req.file.buffer, req.file.originalname);

    } catch (error) {
      console.error(`❌ Upload error for ${req.file?.originalname}:`, error);
      
      // Try to update document status if it was created
      try {
        await DocumentModel.update(documentId, {
          status: 'error',
          error_message: error.message
        });
      } catch (updateError) {
        console.error('Failed to update document status:', updateError);
      }

      res.status(500).json({
        error: 'Upload failed',
        message: error.message || 'An error occurred during file upload'
      });
    }
  }
);

/**
 * Background document processing
 */
async function processDocumentAsync(documentId, fileBuffer, originalName) {
  try {
    console.log(`🔄 Starting background processing for document: ${documentId}`);
    
    // Update status to processing
    await DocumentModel.update(documentId, { status: 'processing' });

    // Step 1: Parse document with LlamaParse
    console.log(`📄 Parsing document: ${originalName}`);
    const parseResult = await llamaParseService.parseFile(fileBuffer, originalName);
    
    if (!parseResult.result?.text) {
      throw new Error('No text content extracted from document');
    }

    // Update with parsed data
    await DocumentModel.update(documentId, {
      parsed_text: parseResult.result.text,
      metadata: parseResult.result.metadata
    });

    // Step 2: Create vector index with LlamaIndex
    console.log(`🔍 Creating vector index for: ${originalName}`);
    const indexResult = await llamaIndexService.createIndex(parseResult.result, originalName);
    
    if (!indexResult.id) {
      throw new Error('Failed to create vector index');
    }

    // Update with index ID and mark as ready
    await DocumentModel.update(documentId, {
      status: 'ready',
      index_id: indexResult.id
    });

    console.log(`✅ Document processing completed: ${documentId}`);

  } catch (error) {
    console.error(`❌ Background processing failed for ${documentId}:`, error);
    
    // Update document with error status
    await DocumentModel.update(documentId, {
      status: 'error',
      error_message: error.message
    });
  }
}

/**
 * Get all documents
 * GET /api/documents
 */
router.get('/', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    const status = req.query.status;

    let documents;
    if (status) {
      documents = await DocumentModel.findByStatus(status);
    } else {
      documents = await DocumentModel.findAll(limit, offset);
    }

    // Format response
    const formattedDocuments = documents.map(doc => ({
      id: doc.id,
      filename: doc.original_name,
      size: doc.file_size,
      mimeType: doc.mime_type,
      status: doc.status,
      metadata: doc.metadata,
      indexId: doc.index_id,
      errorMessage: doc.error_message,
      createdAt: doc.created_at,
      updatedAt: doc.updated_at
    }));

    res.json({
      success: true,
      documents: formattedDocuments,
      total: formattedDocuments.length
    });

  } catch (error) {
    console.error('Get documents error:', error);
    res.status(500).json({
      error: 'Failed to retrieve documents',
      message: error.message
    });
  }
});

/**
 * Get specific document
 * GET /api/documents/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const document = await DocumentModel.findById(req.params.id);
    
    if (!document) {
      return res.status(404).json({
        error: 'Document not found',
        message: `Document with ID ${req.params.id} does not exist`
      });
    }

    // Format response
    const formattedDocument = {
      id: document.id,
      filename: document.original_name,
      size: document.file_size,
      mimeType: document.mime_type,
      status: document.status,
      metadata: document.metadata,
      indexId: document.index_id,
      errorMessage: document.error_message,
      createdAt: document.created_at,
      updatedAt: document.updated_at,
      // Include parsed text only if document is ready
      ...(document.status === 'ready' && { parsedText: document.parsed_text })
    };

    res.json({
      success: true,
      document: formattedDocument
    });

  } catch (error) {
    console.error('Get document error:', error);
    res.status(500).json({
      error: 'Failed to retrieve document',
      message: error.message
    });
  }
});

/**
 * Query document using vector search
 * POST /api/documents/:id/query
 */
router.post('/:id/query', async (req, res) => {
  try {
    const { query, topK = 5 } = req.body;
    
    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        error: 'Invalid query',
        message: 'Query parameter is required and must be a string'
      });
    }

    const document = await DocumentModel.findById(req.params.id);
    
    if (!document) {
      return res.status(404).json({
        error: 'Document not found',
        message: `Document with ID ${req.params.id} does not exist`
      });
    }

    if (document.status !== 'ready') {
      return res.status(400).json({
        error: 'Document not ready',
        message: `Document is in ${document.status} status. Please wait for processing to complete.`
      });
    }

    if (!document.index_id) {
      return res.status(400).json({
        error: 'No vector index',
        message: 'Document does not have a vector index for querying'
      });
    }

    // Perform vector search
    console.log(`🔍 Querying document ${req.params.id} with: "${query}"`);
    const searchResults = await llamaIndexService.retrieve(document.index_id, query, topK);

    res.json({
      success: true,
      query: query,
      document: {
        id: document.id,
        filename: document.original_name
      },
      results: searchResults.nodes || [],
      metadata: {
        topK: topK,
        totalResults: searchResults.nodes?.length || 0
      }
    });

  } catch (error) {
    console.error('Query document error:', error);
    res.status(500).json({
      error: 'Query failed',
      message: error.message
    });
  }
});

/**
 * Delete document and its vector index
 * DELETE /api/documents/:id
 */
router.delete('/:id', async (req, res) => {
  try {
    const document = await DocumentModel.findById(req.params.id);
    
    if (!document) {
      return res.status(404).json({
        error: 'Document not found',
        message: `Document with ID ${req.params.id} does not exist`
      });
    }

    // Delete vector index if it exists
    if (document.index_id) {
      try {
        console.log(`🗑️  Deleting vector index: ${document.index_id}`);
        await llamaIndexService.deleteIndex(document.index_id);
      } catch (indexError) {
        console.warn(`Failed to delete vector index ${document.index_id}:`, indexError.message);
        // Continue with document deletion even if index deletion fails
      }
    }

    // Delete document from database
    await DocumentModel.delete(req.params.id);

    console.log(`✅ Document deleted: ${req.params.id}`);

    res.json({
      success: true,
      message: 'Document and associated vector index deleted successfully',
      deletedDocument: {
        id: document.id,
        filename: document.original_name
      }
    });

  } catch (error) {
    console.error('Delete document error:', error);
    res.status(500).json({
      error: 'Failed to delete document',
      message: error.message
    });
  }
});

export default router;
