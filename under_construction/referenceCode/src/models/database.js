import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const DATABASE_PATH = process.env.DATABASE_PATH || join(__dirname, '../../data/documents.db');

// Ensure data directory exists
const dataDir = dirname(DATABASE_PATH);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Create database connection
const db = new sqlite3.Database(DATABASE_PATH, (err) => {
  if (err) {
    console.error('Error opening database:', err);
  } else {
    console.log('Connected to SQLite database');
  }
});

// Initialize database tables
export async function initializeDatabase() {
  return new Promise((resolve, reject) => {
    const createDocumentsTable = `
      CREATE TABLE IF NOT EXISTS documents (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        original_name TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        mime_type TEXT NOT NULL,
        status TEXT DEFAULT 'uploading',
        parsed_text TEXT,
        metadata TEXT,
        index_id TEXT,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    const createIndexTable = `
      CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
      CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);
    `;

    db.serialize(() => {
      db.run(createDocumentsTable, (err) => {
        if (err) {
          console.error('Error creating documents table:', err);
          reject(err);
          return;
        }
      });

      db.run(createIndexTable, (err) => {
        if (err) {
          console.error('Error creating indexes:', err);
          reject(err);
          return;
        }
        resolve();
      });
    });
  });
}

// Document CRUD operations
export const DocumentModel = {
  // Create new document
  create: (documentData) => {
    return new Promise((resolve, reject) => {
      const {
        id,
        filename,
        originalName,
        fileSize,
        mimeType,
        status = 'uploading'
      } = documentData;

      const sql = `
        INSERT INTO documents (id, filename, original_name, file_size, mime_type, status)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      db.run(sql, [id, filename, originalName, fileSize, mimeType, status], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, ...documentData });
        }
      });
    });
  },

  // Get document by ID
  findById: (id) => {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM documents WHERE id = ?';
      
      db.get(sql, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          if (row && row.metadata) {
            try {
              row.metadata = JSON.parse(row.metadata);
            } catch (e) {
              console.warn('Failed to parse metadata for document:', id);
            }
          }
          resolve(row);
        }
      });
    });
  },

  // Get all documents
  findAll: (limit = 50, offset = 0) => {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM documents 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `;
      
      db.all(sql, [limit, offset], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const documents = rows.map(row => {
            if (row.metadata) {
              try {
                row.metadata = JSON.parse(row.metadata);
              } catch (e) {
                console.warn('Failed to parse metadata for document:', row.id);
              }
            }
            return row;
          });
          resolve(documents);
        }
      });
    });
  },

  // Update document
  update: (id, updates) => {
    return new Promise((resolve, reject) => {
      const fields = [];
      const values = [];

      Object.keys(updates).forEach(key => {
        if (key === 'metadata' && typeof updates[key] === 'object') {
          fields.push(`${key} = ?`);
          values.push(JSON.stringify(updates[key]));
        } else {
          fields.push(`${key} = ?`);
          values.push(updates[key]);
        }
      });

      fields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(id);

      const sql = `UPDATE documents SET ${fields.join(', ')} WHERE id = ?`;

      db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, changes: this.changes });
        }
      });
    });
  },

  // Delete document
  delete: (id) => {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM documents WHERE id = ?';
      
      db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, changes: this.changes });
        }
      });
    });
  },

  // Get documents by status
  findByStatus: (status) => {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM documents WHERE status = ? ORDER BY created_at DESC';
      
      db.all(sql, [status], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
};

export default db;
