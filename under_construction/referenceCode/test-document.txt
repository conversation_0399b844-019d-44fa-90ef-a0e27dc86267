# RAG System Test Document

This is a comprehensive test document for the RAG (Retrieval-Augmented Generation) backend service.

## Introduction

The RAG system is designed to process documents through multiple stages:
1. Document upload and validation
2. Text extraction using LlamaParse
3. Vector embedding creation with LlamaCloud
4. Semantic search capabilities

## Technical Architecture

### Backend Components
- Express.js server with TypeScript
- Multer for file upload handling
- SQLite database for metadata storage
- LlamaParse service for document processing
- LlamaCloud service for vector indexing

### Frontend Components
- React 18 with modern hooks
- Vite for fast development
- Tailwind CSS for styling
- Axios for API communication
- React Router for navigation

## Document Processing Pipeline

The system follows this workflow:
1. **File Upload**: Users can upload PDF, Word, PowerPoint, Excel, and text files
2. **Validation**: Files are validated for type, size, and security
3. **Parsing**: LlamaParse extracts text content and metadata
4. **Indexing**: LlamaCloud creates searchable vector embeddings
5. **Search**: Users can query documents using natural language

## Security Features

The system implements several security measures:
- File type validation with magic number checking
- Size limits (5MB maximum)
- Rate limiting (20 uploads per hour per IP)
- Path traversal protection
- Error message sanitization
- CORS configuration

## Supported File Formats

- PDF documents (.pdf)
- Microsoft Word (.doc, .docx)
- PowerPoint presentations (.ppt, .pptx)
- Excel spreadsheets (.xls, .xlsx)
- Text files (.txt, .md, .html, .rtf, .xml, .csv)

## API Endpoints

### Document Management
- POST /api/documents/upload - Upload new document
- GET /api/documents - List all documents
- GET /api/documents/:id - Get specific document
- DELETE /api/documents/:id - Delete document

### Search and Query
- POST /api/documents/:id/query - Search within document
- GET /health - System health check

## Performance Characteristics

Processing times vary based on document size and complexity:
- Small files (< 1MB): 30-60 seconds
- Medium files (1-3MB): 1-3 minutes
- Large files (3-5MB): 3-5 minutes

## Error Handling

The system provides comprehensive error handling:
- User-friendly error messages
- Detailed logging for debugging
- Graceful fallback mechanisms
- Retry logic for transient failures

## Testing Scenarios

This document can be used to test:
1. Text extraction accuracy
2. Metadata parsing (page count, word count)
3. Vector search relevance
4. Query response quality
5. Error handling robustness

## Sample Queries

Users can test the system with queries like:
- "What is the RAG system?"
- "How does document processing work?"
- "What file formats are supported?"
- "What are the security features?"
- "How long does processing take?"

## Conclusion

This test document provides comprehensive content for validating the RAG system's functionality, from upload through search capabilities. The system should successfully extract this text, create vector embeddings, and enable semantic search across the content.

The document contains approximately 500 words and covers multiple topics, making it ideal for testing the system's ability to handle diverse content and respond to various types of queries.
