# RAG Backend Service with React Frontend

Complete RAG (Retrieval-Augmented Generation) system with backend API and React frontend for document upload, parsing, and vector search.

## 🚀 Features

### Backend
- **Document Upload**: Secure file upload with validation
- **Document Parsing**: Text extraction using LlamaParse API
- **Vector Indexing**: Create searchable vector embeddings with LlamaCloud
- **Vector Search**: Query documents using semantic search
- **Security**: Rate limiting, file validation, and error sanitization
- **Database**: SQLite for document metadata storage

### Frontend
- **React Interface**: Modern, responsive web interface
- **Drag & Drop Upload**: Easy file upload with progress tracking
- **Document Management**: View, search, and organize documents
- **Real-time Search**: Query documents using natural language
- **Status Tracking**: Live updates on document processing

## 📋 Supported File Types

- **PDF**: `.pdf`
- **Microsoft Office**: `.doc`, `.docx`, `.ppt`, `.pptx`, `.xls`, `.xlsx`
- **Text Files**: `.txt`, `.md`, `.html`, `.rtf`, `.xml`, `.csv`

## 🛠️ Installation

### Backend Setup

1. **Install backend dependencies**:
```bash
npm install
```

2. **Environment setup**:
```bash
cp .env.example .env
```

3. **Configure environment variables**:
```env
LLAMA_CLOUD_API_KEY=your_llamacloud_api_key_here
PORT=3000
NODE_ENV=development
```

4. **Start the backend server**:
```bash
# Development
npm run dev

# Production
npm start
```

### Frontend Setup

1. **Navigate to frontend directory**:
```bash
cd frontend
```

2. **Install frontend dependencies**:
```bash
npm install
```

3. **Configure frontend environment**:
```bash
cp .env.example .env
```

4. **Start the frontend development server**:
```bash
npm run dev
```

The frontend will be available at `http://localhost:5173`
The backend API will be available at `http://localhost:3000`

## 🌐 Access URLs

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3000
- **API Health**: http://localhost:3000/health

## 📚 API Endpoints

### Health Check
```http
GET /health
```

### Document Upload
```http
POST /api/documents/upload
Content-Type: multipart/form-data

file: [binary file data]
```

**Response**:
```json
{
  "success": true,
  "message": "File uploaded successfully. Processing started.",
  "document": {
    "id": "uuid",
    "filename": "document.pdf",
    "size": 1024000,
    "status": "uploading",
    "created_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### List Documents
```http
GET /api/documents?limit=50&offset=0&status=ready
```

**Response**:
```json
{
  "success": true,
  "documents": [
    {
      "id": "uuid",
      "filename": "document.pdf",
      "size": 1024000,
      "status": "ready",
      "metadata": {
        "page_count": 10,
        "word_count": 5000
      },
      "indexId": "index_id",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "total": 1
}
```

### Get Document
```http
GET /api/documents/:id
```

### Query Document
```http
POST /api/documents/:id/query
Content-Type: application/json

{
  "query": "What is the main topic of this document?",
  "topK": 5
}
```

**Response**:
```json
{
  "success": true,
  "query": "What is the main topic?",
  "document": {
    "id": "uuid",
    "filename": "document.pdf"
  },
  "results": [
    {
      "text": "Relevant text chunk...",
      "metadata": {
        "page": 1,
        "source": "document.pdf"
      },
      "score": 0.85
    }
  ],
  "metadata": {
    "topK": 5,
    "totalResults": 3
  }
}
```

### Delete Document
```http
DELETE /api/documents/:id
```

## 🔄 Document Processing Flow

1. **Upload**: File uploaded via `/api/documents/upload`
2. **Validation**: Security checks and file validation
3. **Storage**: Document metadata saved to database
4. **Parsing**: LlamaParse extracts text content
5. **Indexing**: LlamaCloud creates vector embeddings
6. **Ready**: Document available for querying

## 🔐 Security Features

### File Validation
- Magic number checking
- MIME type validation
- File size limits (5MB default)
- Path traversal protection
- Suspicious file pattern detection

### Rate Limiting
- 20 uploads per hour per IP
- 100 API requests per 15 minutes
- Configurable limits via environment variables

### Error Handling
- Sanitized error messages
- Comprehensive logging
- Graceful failure handling

## 🗄️ Database Schema

### Documents Table
```sql
CREATE TABLE documents (
  id TEXT PRIMARY KEY,
  filename TEXT NOT NULL,
  original_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  mime_type TEXT NOT NULL,
  status TEXT DEFAULT 'uploading',
  parsed_text TEXT,
  metadata TEXT,
  index_id TEXT,
  error_message TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 📊 Document Status

- `uploading`: File uploaded, processing not started
- `processing`: Document being parsed and indexed
- `ready`: Document ready for querying
- `error`: Processing failed (check error_message)

## 🧪 Testing

Create a test file and run:

```bash
# Create test document
echo "This is a test document for RAG processing." > test-document.txt

# Test upload
curl -X POST http://localhost:3000/api/documents/upload \
  -F "file=@test-document.txt"

# Check status
curl http://localhost:3000/api/documents

# Query document (after processing completes)
curl -X POST http://localhost:3000/api/documents/{document-id}/query \
  -H "Content-Type: application/json" \
  -d '{"query": "What is this document about?"}'
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LLAMA_CLOUD_API_KEY` | LlamaCloud API key | Required |
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment | development |
| `DATABASE_PATH` | SQLite database path | ./data/documents.db |
| `UPLOAD_MAX_SIZE` | Max file size in bytes | 5242880 (5MB) |
| `UPLOAD_MAX_FILES` | Max files per request | 10 |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window | 900000 (15min) |
| `RATE_LIMIT_MAX_REQUESTS` | Max requests per window | 100 |

## 🚨 Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request (invalid file, missing parameters) |
| 404 | Document not found |
| 413 | File too large |
| 429 | Rate limit exceeded |
| 500 | Internal server error |

## 📝 Logs

The service provides comprehensive logging:
- File upload events
- Document processing status
- Vector indexing progress
- Error tracking
- Security events

## 🔄 Monitoring

Check service health:
```bash
curl http://localhost:3000/health
```

Monitor document processing:
```bash
curl http://localhost:3000/api/documents?status=processing
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details.
