#!/usr/bin/env node

/**
 * Local Indexing Test - Verify the indexing fix works with local .env API keys
 */

const fs = require('fs');
const path = require('path');

// Configuration for local development
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Demo123!'
};

// Simple test document
const TEST_DOCUMENT = `# Local Indexing Test

This document tests the LlamaIndex indexing fix with local environment variables.

## Key Points
- API keys are configured in .env file
- Using simplified /indexes API endpoint
- Should create real index (not fallback)
- RAG should work with retrieved context

## Expected Results
- Document status: ready
- Index ID: index_document_[timestamp] (not fallback-)
- Error message: null
- Chat should use RAG with relevant sections

This test verifies the indexing fix works correctly.`;

let authToken = '';
let appId = '';

async function makeRequest(method, endpoint, data = null, headers = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };

  if (data && method !== 'GET') {
    if (data instanceof FormData) {
      delete options.headers['Content-Type'];
      options.body = data;
    } else {
      options.body = JSON.stringify(data);
    }
  }

  console.log(`🔄 ${method} ${endpoint}`);
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    
    if (!response.ok) {
      console.error(`❌ ${method} ${endpoint} failed:`, result);
      throw new Error(`HTTP ${response.status}: ${result.message || 'Request failed'}`);
    }
    
    console.log(`✅ ${method} ${endpoint} success`);
    return result;
  } catch (error) {
    console.error(`💥 ${method} ${endpoint} error:`, error.message);
    throw error;
  }
}

async function testLocalIndexing() {
  console.log('🏠 Local Indexing Test');
  console.log('=====================');
  console.log('Testing with .env API keys...\n');

  try {
    // Check if required modules are available
    if (typeof fetch === 'undefined') {
      global.fetch = require('node-fetch');
    }

    // Step 1: Login
    console.log('🔑 Step 1: Authentication');
    try {
      const result = await makeRequest('POST', '/users/auth/login', {
        email: TEST_USER.email,
        password: TEST_USER.password
      });
      authToken = result.access_token;
      console.log('✅ Login successful\n');
    } catch (error) {
      console.log('⚠️  Login failed, user may not exist. This is expected for first run.\n');
      return;
    }

    // Step 2: Create Application
    console.log('🏗️  Step 2: Create Application');
    const appResult = await makeRequest('POST', '/users/app/create', {
      name: 'Local Indexing Test',
      description: 'Testing indexing fix with local .env keys'
    }, {
      'Authorization': `Bearer ${authToken}`
    });
    appId = appResult.data.id;
    console.log(`📱 Application created: ${appId}\n`);

    // Step 3: Setup ChatAI
    console.log('🤖 Step 3: Setup ChatAI');
    await makeRequest('POST', '/users/app/chatai/setup', {
      appId: appId,
      name: 'Local Test Project',
      description: 'Testing local indexing'
    }, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log('✅ ChatAI setup complete\n');

    // Step 4: Upload Document
    console.log('📄 Step 4: Upload Test Document');
    const testFilePath = path.join(__dirname, 'test-local-document.txt');
    fs.writeFileSync(testFilePath, TEST_DOCUMENT);

    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath));
    formData.append('appId', appId);
    formData.append('name', 'Local Indexing Test Document');
    formData.append('description', 'Testing indexing with .env API keys');

    const uploadResult = await makeRequest('POST', '/users/app/chatai/upload-document', formData, {
      'Authorization': `Bearer ${authToken}`,
      ...formData.getHeaders()
    });

    const documentId = uploadResult.data.id;
    console.log(`📋 Document uploaded: ${documentId}`);
    fs.unlinkSync(testFilePath); // Clean up
    console.log('');

    // Step 5: Monitor Processing
    console.log('⏳ Step 5: Monitor Document Processing');
    const maxAttempts = 15; // 2.5 minutes
    let attempts = 0;
    let finalDocument = null;

    while (attempts < maxAttempts) {
      const result = await makeRequest('GET', `/users/app/chatai/get-documents?appId=${appId}`, null, {
        'Authorization': `Bearer ${authToken}`
      });

      const document = result.data.documents.find(doc => doc.id === documentId);
      
      if (document) {
        console.log(`📊 Status: ${document.status}`);
        
        if (document.status === 'ready') {
          finalDocument = document;
          break;
        } else if (document.status === 'error') {
          console.error('❌ Processing failed:', document.errorMessage);
          return;
        }
      }

      attempts++;
      console.log(`⏱️  Waiting... (${attempts}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, 10000));
    }

    // Step 6: Analyze Results
    console.log('\n📋 Test Results');
    console.log('===============');

    if (finalDocument) {
      console.log(`Status: ${finalDocument.status}`);
      console.log(`Index ID: ${finalDocument.indexId || 'None'}`);
      console.log(`Error Message: ${finalDocument.errorMessage || 'None'}`);

      if (finalDocument.indexId && !finalDocument.indexId.startsWith('fallback-')) {
        console.log('\n🎉 SUCCESS: Real LlamaCloud indexing working!');
        console.log('✅ API keys from .env file are working correctly');
        console.log('✅ Indexing fix is successful');
        console.log('✅ Ready for RAG-powered chat');
      } else if (finalDocument.indexId && finalDocument.indexId.startsWith('fallback-')) {
        console.log('\n⚠️  FALLBACK: Using fallback indexing');
        console.log('🔑 Check if API keys in .env are valid');
        console.log('📝 Document processed but not indexed in LlamaCloud');
      } else {
        console.log('\n❌ FAILED: No index created');
        console.log('💥 Something went wrong with the indexing process');
      }
    } else {
      console.log('\n⏰ TIMEOUT: Document processing took too long');
      console.log('🔄 Try running the test again');
    }

  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the NestJS server is running (npm run start:dev)');
    console.log('2. Check if .env file has valid API keys');
    console.log('3. Verify database connection is working');
  }
}

// Run the test
if (require.main === module) {
  testLocalIndexing();
}

module.exports = { testLocalIndexing };
