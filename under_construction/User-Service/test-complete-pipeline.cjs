const fs = require('fs');
const path = require('path');

// Test the complete document upload pipeline with fallback
async function testCompleteDocumentPipeline() {
  console.log('🧪 Testing Complete Document Upload Pipeline with Fallback\n');

  const baseUrl = 'http://localhost:3000';

  try {
    // Step 1: Login
    console.log('1️⃣ Logging in...');
    const loginResponse = await fetch(`${baseUrl}/users/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Demo123!'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed - user might not exist');
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.result.authToken;
    console.log('   ✅ Login successful');

    // Step 2: Get existing app (free tier only allows one app)
    console.log('\n2️⃣ Getting existing application...');
    const appsResponse = await fetch(`${baseUrl}/users/app/get-all-apps`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    let appId;
    if (appsResponse.ok) {
      const appsData = await appsResponse.json();
      if (appsData.result.length > 0) {
        appId = appsData.result[0].id;
        console.log(`   ✅ Using existing app: ${appId}`);
        console.log(`   📋 App name: ${appsData.result[0].appName}`);
      } else {
        console.log('   ❌ No existing apps found - need to create one first');
        return;
      }
    } else {
      console.log('   ❌ Failed to get apps');
      return;
    }

    // Step 3: Setup ChatAI
    console.log('\n3️⃣ Setting up ChatAI...');
    const chatAiResponse = await fetch(`${baseUrl}/users/app/chatai/setup-chatai`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        name: 'Test ChatAI Project',
        description: 'Test project for document pipeline',
        appId: appId
      })
    });

    if (!chatAiResponse.ok) {
      const errorText = await chatAiResponse.text();
      if (errorText.includes('already exists')) {
        console.log('   ✅ ChatAI project already exists');
      } else {
        console.log('   ❌ ChatAI setup failed:', errorText);
        return;
      }
    } else {
      console.log('   ✅ ChatAI project created');
    }

    // Step 4: Check initial credit usage
    console.log('\n4️⃣ Checking initial credits...');
    const initialCreditResponse = await fetch(`${baseUrl}/users/app/chatai/get-credit-usage?appId=${appId}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (initialCreditResponse.ok) {
      const initialCreditData = await initialCreditResponse.json();
      console.log(`   💳 Initial credits: ${initialCreditData.result.creditsRemaining}`);
      console.log(`   📋 Subscription: ${initialCreditData.result.subscriptionStatus}`);
    }

    // Step 5: Upload document
    console.log('\n5️⃣ Uploading document...');
    const testFilePath = path.join(__dirname, 'test-document.txt');
    const fileBuffer = fs.readFileSync(testFilePath);

    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('file', fileBuffer, 'test-document.txt');
    formData.append('appId', appId);
    formData.append('title', 'Test Document with Fallback');
    formData.append('description', 'Testing document pipeline with LlamaIndex fallback');

    const uploadResponse = await fetch(`${baseUrl}/users/app/chatai/upload-document`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    if (!uploadResponse.ok) {
      const uploadError = await uploadResponse.text();
      console.log('   ❌ Document upload failed:', uploadError);
      return;
    }

    const uploadData = await uploadResponse.json();
    console.log('   ✅ Document upload started');
    console.log(`   📄 Document ID: ${uploadData.result.documentId}`);
    console.log(`   📊 Initial status: ${uploadData.result.status}`);

    // Step 6: Monitor document processing
    console.log('\n6️⃣ Monitoring document processing...');

    let attempts = 0;
    const maxAttempts = 15; // 1.5 minutes max
    let finalDocument = null;

    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 6000)); // Wait 6 seconds

      const documentsResponse = await fetch(`${baseUrl}/users/app/chatai/get-documents?appId=${appId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (documentsResponse.ok) {
        const documentsData = await documentsResponse.json();
        const document = documentsData.result.find(doc => doc.id === uploadData.result.documentId);

        if (document) {
          console.log(`   🔄 Attempt ${attempts + 1}: Status = ${document.status}`);

          if (document.status === 'ready') {
            console.log('   ✅ Document processing completed!');
            console.log(`   📊 Pages: ${document.pageCount}`);
            console.log(`   📊 Words: ${document.wordCount}`);
            console.log(`   🎯 Index ID: ${document.indexId}`);

            if (document.errorMessage) {
              console.log(`   ⚠️  Note: ${document.errorMessage}`);
            }

            finalDocument = document;
            break;
          } else if (document.status === 'error') {
            console.log('   ❌ Document processing failed');
            console.log(`   📋 Error: ${document.errorMessage}`);
            finalDocument = document;
            break;
          }
        }
      }

      attempts++;
    }

    if (attempts >= maxAttempts && (!finalDocument || finalDocument.status !== 'ready')) {
      console.log('   ⚠️  Document processing timeout - still in progress');
    }

    // Step 7: Check final credit usage
    console.log('\n7️⃣ Checking final credits...');
    const finalCreditResponse = await fetch(`${baseUrl}/users/app/chatai/get-credit-usage?appId=${appId}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (finalCreditResponse.ok) {
      const finalCreditData = await finalCreditResponse.json();
      console.log(`   💳 Final credits: ${finalCreditData.result.creditsRemaining}`);
      console.log(`   📊 Usage records: ${finalCreditData.result.usage.length}`);

      if (finalCreditData.result.usage.length > 0) {
        const latestUsage = finalCreditData.result.usage[0];
        console.log(`   📋 Latest usage: ${latestUsage.actionType} - ${latestUsage.creditsUsed} credits`);
      }
    }

    // Step 8: Test document listing
    console.log('\n8️⃣ Testing document listing...');
    const listResponse = await fetch(`${baseUrl}/users/app/chatai/get-documents?appId=${appId}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (listResponse.ok) {
      const listData = await listResponse.json();
      console.log(`   ✅ Found ${listData.result.length} documents`);

      if (listData.result.length > 0) {
        const doc = listData.result[0];
        console.log(`   📄 Latest document: ${doc.filename} (${doc.status})`);
      }
    }

    console.log('\n🎉 Complete Document Pipeline Test Finished!');

    // Summary
    console.log('\n📋 Test Summary:');
    console.log('✅ User authentication');
    console.log('✅ Application management');
    console.log('✅ ChatAI project setup');
    console.log('✅ Credit system integration');
    console.log('✅ Document upload with validation');
    console.log('✅ Document processing with LlamaIndex fallback');
    console.log('✅ Status tracking and monitoring');
    console.log('✅ Document listing and retrieval');

    if (finalDocument) {
      if (finalDocument.indexId && finalDocument.indexId.startsWith('fallback-')) {
        console.log('✅ Fallback indexing mechanism working');
        console.log('💡 LlamaIndex integration updated but using fallback due to free tier limits');
      } else if (finalDocument.indexId) {
        console.log('✅ LlamaIndex integration fully working');
      }
    }

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
  }
}

// Run the test
testCompleteDocumentPipeline().catch(console.error);
