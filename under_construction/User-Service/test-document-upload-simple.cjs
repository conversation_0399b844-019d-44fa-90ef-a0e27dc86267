const fs = require('fs');
const FormData = require('form-data');

// Simple document upload test
async function testDocumentUpload() {
  console.log('🧪 Testing Document Upload\n');

  const baseUrl = 'http://localhost:3000';
  
  try {
    // Step 1: Login
    console.log('1️⃣ Logging in...');
    const loginResponse = await fetch(`${baseUrl}/users/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Demo123!'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed');
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.result.authToken;
    console.log('   ✅ Login successful');

    // Step 2: Get app
    console.log('\n2️⃣ Getting app...');
    const appsResponse = await fetch(`${baseUrl}/users/app/get-all-apps`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (!appsResponse.ok) {
      console.log('❌ Failed to get apps');
      return;
    }

    const appsData = await appsResponse.json();
    const appId = appsData.result[0].id;
    console.log(`   ✅ Using app: ${appId}`);

    // Step 3: Upload document
    console.log('\n3️⃣ Uploading document...');
    
    // Read the test file
    const fileBuffer = fs.readFileSync('./test-document.txt');
    
    // Create form data
    const formData = new FormData();
    formData.append('file', fileBuffer, {
      filename: 'test-document.txt',
      contentType: 'text/plain'
    });
    formData.append('appId', appId);
    formData.append('title', 'Test Document');
    formData.append('description', 'Testing document upload with fallback');

    console.log('   📄 File size:', fileBuffer.length, 'bytes');
    console.log('   📋 App ID:', appId);

    const uploadResponse = await fetch(`${baseUrl}/users/app/chatai/upload-document`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    console.log('   📊 Upload status:', uploadResponse.status);

    if (uploadResponse.ok) {
      const uploadData = await uploadResponse.json();
      console.log('   ✅ Document upload successful!');
      console.log('   📋 Response:', JSON.stringify(uploadData, null, 2));
      
      const documentId = uploadData.result.documentId;
      
      // Step 4: Monitor processing
      console.log('\n4️⃣ Monitoring document processing...');
      
      let attempts = 0;
      const maxAttempts = 10;
      
      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
        
        const documentsResponse = await fetch(`${baseUrl}/users/app/chatai/get-documents?appId=${appId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (documentsResponse.ok) {
          const documentsData = await documentsResponse.json();
          const document = documentsData.result.find(doc => doc.id === documentId);
          
          if (document) {
            console.log(`   🔄 Attempt ${attempts + 1}: Status = ${document.status}`);
            
            if (document.status === 'ready') {
              console.log('   ✅ Document processing completed!');
              console.log('   📊 Final document:', JSON.stringify(document, null, 2));
              break;
            } else if (document.status === 'error') {
              console.log('   ❌ Document processing failed');
              console.log('   📋 Error:', document.errorMessage);
              break;
            }
          }
        }
        
        attempts++;
      }

      if (attempts >= maxAttempts) {
        console.log('   ⚠️  Processing timeout - still in progress');
      }

    } else {
      const uploadError = await uploadResponse.text();
      console.log('   ❌ Document upload failed:', uploadError);
    }

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testDocumentUpload().catch(console.error);
