#!/usr/bin/env node

/**
 * Test script to verify LlamaIndex indexing fix
 * This script tests the document upload and indexing pipeline
 */

const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Demo123!'
};

// Test document content
const TEST_DOCUMENT_CONTENT = `# LlamaIndex Indexing Test Document

This document is specifically designed to test the LlamaIndex indexing functionality.

## Key Information
- Document processing pipeline
- Vector database indexing
- RAG (Retrieval Augmented Generation)
- LlamaCloud API integration

## Technical Details
The indexing process involves:
1. Document parsing with LlamaParse
2. Vector embedding generation
3. Storage in LlamaCloud vector database
4. Retrieval for chat context

## Test Questions
- What is this document about?
- How does the indexing process work?
- What are the technical details mentioned?

This content should be properly indexed and retrievable through the chat system.`;

let authToken = '';
let appId = '';
let documentId = '';

async function makeRequest(method, endpoint, data = null, headers = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };

  if (data && method !== 'GET') {
    if (data instanceof FormData) {
      delete options.headers['Content-Type'];
      options.body = data;
    } else {
      options.body = JSON.stringify(data);
    }
  }

  console.log(`🔄 ${method} ${endpoint}`);
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    
    if (!response.ok) {
      console.error(`❌ ${method} ${endpoint} failed:`, result);
      throw new Error(`HTTP ${response.status}: ${result.message || 'Request failed'}`);
    }
    
    console.log(`✅ ${method} ${endpoint} success`);
    return result;
  } catch (error) {
    console.error(`💥 ${method} ${endpoint} error:`, error.message);
    throw error;
  }
}

async function login() {
  console.log('\n🔑 Step 1: Authentication');
  
  try {
    const result = await makeRequest('POST', '/users/auth/login', {
      email: TEST_USER.email,
      password: TEST_USER.password
    });
    
    authToken = result.access_token;
    console.log('✅ Login successful');
    return true;
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    return false;
  }
}

async function createApp() {
  console.log('\n🏗️  Step 2: Create Application');
  
  const result = await makeRequest('POST', '/users/app/create', {
    name: 'Indexing Test App',
    description: 'Test application for indexing verification'
  }, {
    'Authorization': `Bearer ${authToken}`
  });
  
  appId = result.data.id;
  console.log(`📱 Application created: ${appId}`);
  return true;
}

async function setupChatAI() {
  console.log('\n🤖 Step 3: Setup ChatAI');
  
  await makeRequest('POST', '/users/app/chatai/setup', {
    appId: appId,
    name: 'Indexing Test Project',
    description: 'Testing LlamaIndex integration'
  }, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('✅ ChatAI setup complete');
  return true;
}

async function uploadDocument() {
  console.log('\n📄 Step 4: Upload Test Document');
  
  // Create test file
  const testFilePath = path.join(__dirname, 'test-indexing-document.txt');
  fs.writeFileSync(testFilePath, TEST_DOCUMENT_CONTENT);
  
  // Create FormData for file upload
  const FormData = require('form-data');
  const formData = new FormData();
  formData.append('file', fs.createReadStream(testFilePath));
  formData.append('appId', appId);
  formData.append('name', 'Indexing Test Document');
  formData.append('description', 'Document to test LlamaIndex integration');
  
  try {
    const result = await makeRequest('POST', '/users/app/chatai/upload-document', formData, {
      'Authorization': `Bearer ${authToken}`,
      ...formData.getHeaders()
    });
    
    documentId = result.data.id;
    console.log(`📋 Document uploaded: ${documentId}`);
    
    // Clean up test file
    fs.unlinkSync(testFilePath);
    
    return true;
  } catch (error) {
    // Clean up test file even on error
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
    throw error;
  }
}

async function waitForProcessing() {
  console.log('\n⏳ Step 5: Monitor Document Processing');
  
  const maxAttempts = 20; // 3+ minutes
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    const result = await makeRequest('GET', `/users/app/chatai/get-documents?appId=${appId}`, null, {
      'Authorization': `Bearer ${authToken}`
    });
    
    const document = result.data.documents.find(doc => doc.id === documentId);
    
    if (document) {
      console.log(`📊 Status: ${document.status}`);
      
      if (document.indexId) {
        console.log(`🔗 Index ID: ${document.indexId}`);
        
        // Check if it's a real index or fallback
        if (document.indexId.startsWith('fallback-')) {
          console.log('⚠️  Using fallback indexing (LlamaCloud API may not be working)');
        } else {
          console.log('✅ Real LlamaCloud index created!');
        }
      }
      
      if (document.errorMessage) {
        console.log(`⚠️  Error: ${document.errorMessage}`);
      }
      
      if (document.status === 'ready') {
        console.log('🎉 Document processing complete!');
        
        // Check indexing success
        if (document.indexId && !document.indexId.startsWith('fallback-')) {
          console.log('✅ INDEXING SUCCESS: Document properly indexed in LlamaCloud');
          return { success: true, indexed: true, document };
        } else {
          console.log('⚠️  INDEXING FALLBACK: Document stored but not indexed');
          return { success: true, indexed: false, document };
        }
      } else if (document.status === 'error') {
        console.error('❌ Document processing failed:', document.errorMessage);
        return { success: false, indexed: false, document };
      }
    }
    
    attempts++;
    console.log(`⏱️  Waiting... (${attempts}/${maxAttempts})`);
    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
  }
  
  console.error('⏰ Timeout waiting for document processing');
  return { success: false, indexed: false };
}

async function testIndexingFix() {
  console.log('🔧 LlamaIndex Indexing Fix Test');
  console.log('================================');
  
  try {
    // Check if required modules are available
    if (typeof fetch === 'undefined') {
      global.fetch = require('node-fetch');
    }
    
    const loginSuccess = await login();
    if (!loginSuccess) {
      console.log('❌ Cannot proceed without authentication');
      process.exit(1);
    }
    
    await createApp();
    await setupChatAI();
    await uploadDocument();
    
    const result = await waitForProcessing();
    
    console.log('\n📋 Test Results:');
    console.log('================');
    
    if (result.success && result.indexed) {
      console.log('🎉 INDEXING FIX SUCCESSFUL!');
      console.log('✅ Document properly indexed in LlamaCloud');
      console.log('✅ No fallback indexing used');
      console.log('✅ Ready for RAG-powered chat');
    } else if (result.success && !result.indexed) {
      console.log('⚠️  INDEXING USING FALLBACK');
      console.log('📝 Document processed but not indexed');
      console.log('🔑 Check LLAMA_CLOUD_API_KEY configuration');
    } else {
      console.log('❌ INDEXING FAILED');
      console.log('💥 Document processing failed completely');
    }
    
    if (result.document) {
      console.log('\n📊 Document Details:');
      console.log(`   Status: ${result.document.status}`);
      console.log(`   Index ID: ${result.document.indexId || 'None'}`);
      console.log(`   Error: ${result.document.errorMessage || 'None'}`);
    }
    
  } catch (error) {
    console.error('\n💥 Test failed with error:', error.message);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testIndexingFix();
}

module.exports = { testIndexingFix };
