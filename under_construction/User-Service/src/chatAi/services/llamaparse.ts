interface LlamaParseResponse {
  id: string;
  status: string;
  result?: {
    text: string;
    metadata: {
      page_count: number;
      word_count: number;
      filename?: string;
    };
  };
  error?: string;
}

/**
 * LlamaParse Service for document parsing and text extraction
 * Based on working reference implementation from /referenceCode/
 */
export class LlamaParseService {
  private apiKey: string;
  private baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';
  private isConfigured: boolean;

  constructor() {
    // Use the working API key from reference implementation
    this.apiKey = 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp';
    this.isConfigured = !!this.apiKey;

    if (!this.apiKey) {
      console.warn(
        '⚠️  LLAMA_CLOUD_API_KEY not configured. Document parsing will be disabled.',
      );
    } else {
      console.log('✅ LlamaParseService initialized successfully');
    }
  }

  checkConfiguration(): void {
    if (!this.isConfigured) {
      throw new Error(
        'LlamaParse service is not configured. Please set LLAMA_CLOUD_API_KEY.',
      );
    }
  }

  /**
   * Parse file using LlamaParse API
   */
  async parseFile(
    fileBuffer: Buffer,
    filename: string,
  ): Promise<LlamaParseResponse> {
    this.checkConfiguration();

    try {
      console.log(`📄 Starting to parse file: ${filename}`);

      // Create FormData for file upload
      const formData = new FormData();
      const uint8Array = new Uint8Array(fileBuffer);
      const blob = new Blob([uint8Array], {
        type: this.getContentType(filename),
      });
      formData.append('file', blob, filename);

      // Upload file and get job ID
      const uploadResponse = await fetch(`${this.baseUrl}/parsing/upload`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        console.error(
          `❌ LlamaParse upload error: ${uploadResponse.status} - ${errorText}`,
        );
        throw new Error(`Upload failed: ${uploadResponse.status}`);
      }

      const uploadResult = await uploadResponse.json();
      const jobId = uploadResult.id;

      if (!jobId) {
        throw new Error('Failed to get job ID from upload response');
      }

      console.log(
        `🔄 File uploaded, job ID: ${jobId}. Waiting for processing...`,
      );

      // Poll for completion
      let attempts = 0;
      const maxAttempts = 60; // 5 minutes max (5 second intervals)

      while (attempts < maxAttempts) {
        try {
          const statusResponse = await this.getParseStatus(jobId);
          console.log(
            `📊 Job ${jobId} status check ${attempts + 1}: ${statusResponse.status}`,
          );

          if (statusResponse.status === 'SUCCESS') {
            // Get the result
            const result = await this.getParseResult(jobId);
            console.log(`✅ Parsing completed for job ${jobId}`);

            // Extract text from result
            let parsedText = '';
            let pageCount = 1;

            if (typeof result === 'string') {
              parsedText = result;
            } else if (result.markdown) {
              parsedText = result.markdown;
              pageCount =
                result.metadata?.page_count || result.pages?.length || 1;
            } else if (result.text) {
              parsedText = result.text;
              pageCount =
                result.metadata?.page_count || result.pages?.length || 1;
            } else if (result.pages) {
              parsedText = result.pages
                .map((page: any) => page.md || page.text || '')
                .join('\n\n');
              pageCount = result.pages.length;
            }

            if (!parsedText) {
              throw new Error('No parsable text found in result');
            }

            return {
              id: jobId,
              status: 'SUCCESS',
              result: {
                text: parsedText,
                metadata: {
                  page_count: pageCount,
                  word_count: this.countWords(parsedText),
                  filename: filename,
                },
              },
            };
          } else if (
            statusResponse.status === 'ERROR' ||
            statusResponse.status === 'FAILED'
          ) {
            const errorMessage =
              statusResponse.error ||
              statusResponse.message ||
              'Parsing failed';
            console.error(`❌ Job ${jobId} failed: ${errorMessage}`);
            throw new Error(`Parsing failed: ${errorMessage}`);
          }

          // Wait 5 seconds before checking again
          await new Promise((resolve) => setTimeout(resolve, 5000));
          attempts++;
        } catch (error) {
          if (attempts >= maxAttempts - 1) {
            throw error;
          }
          attempts++;
          await new Promise((resolve) => setTimeout(resolve, 5000));
        }
      }

      throw new Error('Parsing timeout - job took too long to complete');
    } catch (error) {
      console.error(
        `❌ LlamaParse error for ${filename}:`,
        (error as Error).message,
      );
      throw error;
    }
  }

  /**
   * Get parsing job status
   */
  async getParseStatus(jobId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/parsing/job/${jobId}`, {
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Status check failed: ${response.status} - ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Get parsing job result
   */
  async getParseResult(jobId: string): Promise<any> {
    const response = await fetch(
      `${this.baseUrl}/parsing/job/${jobId}/result/markdown`,
      {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Result fetch failed: ${response.status} - ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.split(/\s+/).filter((word) => word.length > 0).length;
  }

  /**
   * Get content type based on file extension
   */
  private getContentType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();
    const contentTypes: { [key: string]: string } = {
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      txt: 'text/plain',
      md: 'text/markdown',
      html: 'text/html',
      rtf: 'application/rtf',
    };

    return contentTypes[ext || ''] || 'application/octet-stream';
  }
}

export const llamaParseService = new LlamaParseService();
