#!/bin/bash

# Test document upload using curl
echo "🧪 Testing Document Upload with curl"

# Step 1: Login
echo "1️⃣ Logging in..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:3000/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Demo123!"}')

echo "Login response: $LOGIN_RESPONSE"

# Extract token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"authToken":"[^"]*"' | cut -d'"' -f4)
echo "Token: ${TOKEN:0:20}..."

# Step 2: Get app
echo -e "\n2️⃣ Getting app..."
APPS_RESPONSE=$(curl -s -X GET http://localhost:3000/users/app/get-all-apps \
  -H "Authorization: Bearer $TOKEN")

echo "Apps response: $APPS_RESPONSE"

# Extract app ID
APP_ID=$(echo $APPS_RESPONSE | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
echo "App ID: $APP_ID"

# Step 3: Upload document
echo -e "\n3️⃣ Uploading document..."
UPLOAD_RESPONSE=$(curl -s -X POST http://localhost:3000/users/app/chatai/upload-document \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@test-document.txt" \
  -F "appId=$APP_ID" \
  -F "title=Test Document" \
  -F "description=Testing document upload")

echo "Upload response: $UPLOAD_RESPONSE"

echo -e "\n✅ Test completed!"
