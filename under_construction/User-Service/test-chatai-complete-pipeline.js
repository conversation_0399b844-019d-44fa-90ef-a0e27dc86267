#!/usr/bin/env node

/**
 * Complete ChatAI Pipeline Test
 * Tests the full document upload → processing → chat pipeline
 */

const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Demo123!',
  username: 'demouser',
  firstName: 'Demo',
  lastName: 'User'
};

// Test document content
const TEST_DOCUMENT_CONTENT = `# ChatAI Test Document

This is a comprehensive test document for the ChatAI system.

## Key Features
- Document upload and processing
- LlamaParse integration for complex files
- Vector indexing with LlamaCloud
- AI-powered summarization
- Real-time chat with RAG

## Technical Details
The ChatAI service implements a complete document processing pipeline:
1. File validation and security checks
2. Background document parsing
3. Vector database indexing
4. AI summarization generation
5. Chat system with context retrieval

## Test Questions
Users should be able to ask:
- What is this document about?
- What are the key features mentioned?
- How does the technical pipeline work?
- What security measures are implemented?

This document contains enough content to test the summarization and chat functionality effectively.`;

let authToken = '';
let appId = '';
let documentId = '';

async function makeRequest(method, endpoint, data = null, headers = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };

  if (data && method !== 'GET') {
    if (data instanceof FormData) {
      delete options.headers['Content-Type']; // Let browser set boundary
      options.body = data;
    } else {
      options.body = JSON.stringify(data);
    }
  }

  console.log(`🔄 ${method} ${endpoint}`);
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    
    if (!response.ok) {
      console.error(`❌ ${method} ${endpoint} failed:`, result);
      throw new Error(`HTTP ${response.status}: ${result.message || 'Request failed'}`);
    }
    
    console.log(`✅ ${method} ${endpoint} success`);
    return result;
  } catch (error) {
    console.error(`💥 ${method} ${endpoint} error:`, error.message);
    throw error;
  }
}

async function step1_Login() {
  console.log('\n📝 Step 1: User Authentication');
  
  try {
    const result = await makeRequest('POST', '/users/auth/login', {
      email: TEST_USER.email,
      password: TEST_USER.password
    });
    
    authToken = result.access_token;
    console.log('🔑 Authentication successful');
    return true;
  } catch (error) {
    console.log('⚠️  Login failed, attempting registration...');
    
    try {
      await makeRequest('POST', '/users/auth/register', TEST_USER);
      console.log('👤 User registered successfully');
      
      const loginResult = await makeRequest('POST', '/users/auth/login', {
        email: TEST_USER.email,
        password: TEST_USER.password
      });
      
      authToken = loginResult.access_token;
      console.log('🔑 Authentication successful after registration');
      return true;
    } catch (regError) {
      console.error('❌ Registration and login failed:', regError.message);
      return false;
    }
  }
}

async function step2_CreateApplication() {
  console.log('\n🏗️  Step 2: Create Application');
  
  const result = await makeRequest('POST', '/users/app/create', {
    name: 'ChatAI Test App',
    description: 'Test application for ChatAI pipeline'
  }, {
    'Authorization': `Bearer ${authToken}`
  });
  
  appId = result.data.id;
  console.log(`📱 Application created: ${appId}`);
  return true;
}

async function step3_SetupChatAI() {
  console.log('\n🤖 Step 3: Setup ChatAI Project');
  
  const result = await makeRequest('POST', '/users/app/chatai/setup', {
    appId: appId,
    name: 'Test ChatAI Project',
    description: 'Testing complete document processing pipeline'
  }, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log('🎯 ChatAI project setup complete');
  return true;
}

async function step4_UploadDocument() {
  console.log('\n📄 Step 4: Upload Test Document');
  
  // Create test file
  const testFilePath = path.join(__dirname, 'test-chatai-document.txt');
  fs.writeFileSync(testFilePath, TEST_DOCUMENT_CONTENT);
  
  // Create FormData for file upload
  const FormData = require('form-data');
  const formData = new FormData();
  formData.append('file', fs.createReadStream(testFilePath));
  formData.append('appId', appId);
  formData.append('name', 'ChatAI Test Document');
  formData.append('description', 'Test document for pipeline verification');
  
  try {
    const result = await makeRequest('POST', '/users/app/chatai/upload-document', formData, {
      'Authorization': `Bearer ${authToken}`,
      ...formData.getHeaders()
    });
    
    documentId = result.data.id;
    console.log(`📋 Document uploaded: ${documentId}`);
    
    // Clean up test file
    fs.unlinkSync(testFilePath);
    
    return true;
  } catch (error) {
    // Clean up test file even on error
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
    throw error;
  }
}

async function step5_WaitForProcessing() {
  console.log('\n⏳ Step 5: Wait for Document Processing');
  
  const maxAttempts = 30; // 5 minutes max
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    const result = await makeRequest('GET', `/users/app/chatai/get-documents?appId=${appId}`, null, {
      'Authorization': `Bearer ${authToken}`
    });
    
    const document = result.data.documents.find(doc => doc.id === documentId);
    
    if (document) {
      console.log(`📊 Document status: ${document.status}`);
      
      if (document.status === 'ready') {
        console.log('✅ Document processing complete!');
        if (document.summary) {
          console.log('📝 AI Summary generated successfully');
        }
        return true;
      } else if (document.status === 'error') {
        console.error('❌ Document processing failed:', document.errorMessage);
        return false;
      }
    }
    
    attempts++;
    console.log(`⏱️  Waiting... (${attempts}/${maxAttempts})`);
    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
  }
  
  console.error('⏰ Timeout waiting for document processing');
  return false;
}

async function step6_TestChat() {
  console.log('\n💬 Step 6: Test Chat Functionality');
  
  const testQuestions = [
    'What is this document about?',
    'What are the key features mentioned?',
    'How does the technical pipeline work?'
  ];
  
  for (const question of testQuestions) {
    console.log(`\n❓ Question: "${question}"`);
    
    try {
      // Note: This would need to be adapted for streaming responses
      const result = await makeRequest('POST', '/users/app/chatai/chat', {
        appId: appId,
        query: question
      }, {
        'Authorization': `Bearer ${authToken}`
      });
      
      console.log('💡 Chat response received successfully');
    } catch (error) {
      console.error(`❌ Chat failed for question: "${question}"`, error.message);
    }
  }
  
  return true;
}

async function step7_GetChatHistory() {
  console.log('\n📚 Step 7: Retrieve Chat History');
  
  const result = await makeRequest('GET', `/users/app/chatai/get-chat-history?appId=${appId}`, null, {
    'Authorization': `Bearer ${authToken}`
  });
  
  console.log(`💬 Chat history: ${result.data.totalMessages} messages`);
  return true;
}

async function runCompleteTest() {
  console.log('🚀 Starting Complete ChatAI Pipeline Test');
  console.log('=====================================');
  
  try {
    // Check if required modules are available
    if (typeof fetch === 'undefined') {
      global.fetch = require('node-fetch');
    }
    
    const success = await step1_Login() &&
                   await step2_CreateApplication() &&
                   await step3_SetupChatAI() &&
                   await step4_UploadDocument() &&
                   await step5_WaitForProcessing() &&
                   await step6_TestChat() &&
                   await step7_GetChatHistory();
    
    if (success) {
      console.log('\n🎉 Complete Pipeline Test PASSED!');
      console.log('✅ All ChatAI features working correctly');
    } else {
      console.log('\n❌ Pipeline Test FAILED');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 Test failed with error:', error.message);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  runCompleteTest();
}

module.exports = { runCompleteTest };
