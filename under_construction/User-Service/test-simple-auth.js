const fetch = require('node-fetch');
require('dotenv').config();

async function testAuth() {
  const baseUrl = 'http://localhost:3000';

  console.log('Testing authentication flow...');

  // First, try to register the demo user
  console.log('\n📝 Registering demo user...');

  const registerResponse = await fetch(`${baseUrl}/users/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Demo123!',
      username: 'demouser',
      firstName: 'Demo',
      lastName: 'User',
    }),
  });

  console.log('Register status:', registerResponse.status);
  const registerResult = await registerResponse.text();
  console.log('Register response:', registerResult);

  // Then login
  console.log('\n🔐 Logging in...');

  const loginResponse = await fetch(`${baseUrl}/users/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Demo123!',
    }),
  });

  console.log('Login status:', loginResponse.status);

  if (!loginResponse.ok) {
    console.error('❌ Login failed:', await loginResponse.text());
    return;
  }

  const loginResult = await loginResponse.json();
  console.log('✅ Login successful');
  console.log('Full login response:', JSON.stringify(loginResult, null, 2));

  // Try to find the token in different possible locations
  const token = loginResult.result?.accessToken ||
    loginResult.accessToken ||
    loginResult.token ||
    loginResult.result?.token;

  if (!token) {
    console.error('❌ No token found in response');
    return;
  }

  console.log('Token type:', typeof token);
  console.log('Token length:', token.length);
  console.log('Token preview:', token.substring(0, 50) + '...');

  // Test a simple authenticated endpoint
  console.log('\n🧪 Testing authenticated endpoint...');

  const testResponse = await fetch(`${baseUrl}/users/app/get-all-apps`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  console.log('Test endpoint status:', testResponse.status);

  if (testResponse.ok) {
    const testResult = await testResponse.json();
    console.log('✅ Authentication working!');
    console.log('Apps found:', testResult.result?.length || 0);
  } else {
    const errorText = await testResponse.text();
    console.error('❌ Authentication failed:', errorText);
  }
}

testAuth().catch(console.error);
