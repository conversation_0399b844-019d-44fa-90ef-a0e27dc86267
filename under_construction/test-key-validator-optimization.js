#!/usr/bin/env node

/**
 * Test script to verify the key-validator optimization
 * This script tests that the key-validator API now includes documents in the response
 * eliminating the need for a separate API call to get documents
 */

const fetch = require('node-fetch');

// Configuration
const USER_SERVICE_BASE_URL = process.env.USER_SERVICE_BASE_URL || 'http://localhost:3001';
const CHATAI_SDK_BASE_URL = process.env.CHATAI_SDK_BASE_URL || 'http://localhost:3002';

// Test API key and origin (replace with actual test values)
const TEST_API_KEY = process.env.TEST_API_KEY || 'test_api_key_1751884336144_vp9gospvg';
const TEST_ORIGIN = 'http://localhost:3000';

async function testKeyValidatorOptimization() {
  console.log('🧪 Testing Key-Validator Optimization');
  console.log('=====================================\n');

  try {
    // Test 1: Call key-validator directly and check if documents are included
    console.log('📋 Test 1: Direct key-validator API call');
    console.log('------------------------------------------');
    
    const requestBody = {
      chainId: 1,
      apikey: TEST_API_KEY,
      origin: TEST_ORIGIN,
      payload: {
        method: 'validate',
        params: []
      },
      type: 'chatai'
    };

    console.log(`🌐 Making request to: ${USER_SERVICE_BASE_URL}/users/app/key-validator`);
    console.log(`📦 Request payload:`, JSON.stringify(requestBody, null, 2));

    const response = await fetch(`${USER_SERVICE_BASE_URL}/users/app/key-validator`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:3002' // ChatAI-SDK origin
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Key validation failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    
    if (result.error) {
      throw new Error(`Key validation error: ${result.message}`);
    }

    console.log(`✅ Key validation successful`);
    console.log(`📊 Credit info:`, result.result.creditInfo);
    
    // Check if documents are included
    const documents = result.result.documents;
    if (documents && Array.isArray(documents)) {
      console.log(`📄 Documents included in response: ${documents.length} documents`);
      console.log(`✅ OPTIMIZATION WORKING: Documents are included in key-validator response!`);
      
      if (documents.length > 0) {
        console.log(`📋 Sample document:`, {
          id: documents[0].id,
          filename: documents[0].filename,
          status: documents[0].status,
          hasIndexId: !!documents[0].indexId
        });
      }
    } else {
      console.log(`❌ OPTIMIZATION NOT WORKING: Documents not found in key-validator response`);
      return false;
    }

    console.log('\n');

    // Test 2: Compare with separate documents API call
    console.log('📋 Test 2: Compare with separate documents API call');
    console.log('----------------------------------------------------');
    
    const appId = result.result.appId;
    if (!appId) {
      throw new Error('AppId not found in validation result');
    }

    console.log(`🌐 Making request to: ${USER_SERVICE_BASE_URL}/users/app/chatai/widget/get-documents?appId=${appId}`);
    
    const documentsResponse = await fetch(`${USER_SERVICE_BASE_URL}/users/app/chatai/widget/get-documents?appId=${appId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!documentsResponse.ok) {
      const errorText = await documentsResponse.text();
      throw new Error(`Documents API failed: ${documentsResponse.status} - ${errorText}`);
    }

    const documentsResult = await documentsResponse.json();
    
    if (documentsResult.error) {
      throw new Error(`Documents API error: ${documentsResult.message}`);
    }

    const separateDocuments = documentsResult.result || [];
    console.log(`📄 Documents from separate API: ${separateDocuments.length} documents`);

    // Compare the results
    const keyValidatorDocs = documents.length;
    const separateApiDocs = separateDocuments.length;
    
    console.log(`\n📊 Comparison Results:`);
    console.log(`   Key-validator documents: ${keyValidatorDocs}`);
    console.log(`   Separate API documents: ${separateApiDocs}`);
    
    if (keyValidatorDocs === separateApiDocs) {
      console.log(`✅ OPTIMIZATION VERIFIED: Both APIs return the same number of documents!`);
      console.log(`⚡ Performance benefit: Eliminated 1 API call per chat request`);
    } else {
      console.log(`⚠️  WARNING: Document counts don't match. This might be due to filtering differences.`);
      console.log(`   Key-validator filters for 'ready' documents with valid indexId`);
      console.log(`   Separate API returns all documents`);
    }

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

async function testChatAISDKIntegration() {
  console.log('\n🧪 Testing ChatAI-SDK Integration');
  console.log('==================================\n');

  try {
    const testQuery = 'What is the main topic of the documents?';
    
    console.log(`🌐 Making request to: ${CHATAI_SDK_BASE_URL}/api/v1/`);
    console.log(`📝 Query: ${testQuery}`);
    
    const response = await fetch(
      `${CHATAI_SDK_BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=${encodeURIComponent(testQuery)}&stream=false`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`ChatAI-SDK request failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    
    if (result.error) {
      throw new Error(`ChatAI-SDK error: ${result.message}`);
    }

    console.log(`✅ ChatAI-SDK request successful`);
    console.log(`📊 Documents used: ${result.documentsUsed}`);
    console.log(`⏱️  Timing:`, result.timing);
    console.log(`💾 Cached:`, result.cached);
    
    if (result.cached && result.cached.apiKey === false) {
      console.log(`✅ OPTIMIZATION WORKING: Fresh API key validation with documents included`);
    }

    return true;

  } catch (error) {
    console.error('❌ ChatAI-SDK test failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting Key-Validator Optimization Tests\n');
  
  const test1Success = await testKeyValidatorOptimization();
  const test2Success = await testChatAISDKIntegration();
  
  console.log('\n📋 Test Summary');
  console.log('================');
  console.log(`Key-validator optimization: ${test1Success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`ChatAI-SDK integration: ${test2Success ? '✅ PASS' : '❌ FAIL'}`);
  
  if (test1Success && test2Success) {
    console.log('\n🎉 All tests passed! The optimization is working correctly.');
    console.log('⚡ Performance improvement: Reduced API calls from 2 to 1 per chat request');
  } else {
    console.log('\n❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testKeyValidatorOptimization,
  testChatAISDKIntegration
};
